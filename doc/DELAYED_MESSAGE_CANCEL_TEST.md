# 延迟消息取消功能测试方案

## 测试目标

验证延迟消息取消机制能够有效解决订单超时库存释放问题。

## 测试环境准备

### 1. Redis数据检查
```bash
# 连接Redis，查看延迟消息队列
redis-cli
> ZRANGE delayed_queue:zset 0 -1 WITHSCORES
> ZCARD delayed_queue:zset
```

### 2. 服务状态检查
```bash
# 检查各服务状态
curl http://localhost:8080/order-services/actuator/health
curl http://localhost:8081/goods-services/actuator/health  
curl http://localhost:8082/delayed-services/actuator/health
```

## 测试用例

### 测试用例1：正常延迟消息取消

**步骤**：
1. 创建订单A，验证延迟消息已添加
2. 手动取消订单A
3. 验证延迟消息已被删除
4. 等待原定超时时间，确认没有超时消息触发

**验证脚本**：
```bash
# 1. 创建订单前检查延迟队列
redis-cli ZCARD delayed_queue:zset

# 2. 创建订单（通过API或界面）
curl -X POST http://localhost:8080/order-services/api/orders \
  -H "Content-Type: application/json" \
  -d '{"userId":1001,"products":[{"sku":"TEST_SKU","size":"M","count":1}]}'

# 3. 验证延迟消息已添加
redis-cli ZCARD delayed_queue:zset
redis-cli ZRANGE delayed_queue:zset 0 -1

# 4. 取消订单
curl -X POST http://localhost:8080/order-services/api/orders/cancel/{orderNo}

# 5. 验证延迟消息已删除
redis-cli ZCARD delayed_queue:zset
redis-cli ZRANGE delayed_queue:zset 0 -1
```

### 测试用例2：并发场景测试

**场景**：同时创建多个订单，然后取消部分订单

**验证要点**：
- 只有被取消订单的延迟消息被删除
- 其他订单的延迟消息保持不变
- 没有误删除其他订单的延迟消息

### 测试用例3：延迟服务不可用场景

**步骤**：
1. 停止delayed-services
2. 尝试取消订单
3. 验证fallback机制是否正常工作
4. 重启delayed-services后验证功能恢复

### 测试用例4：边界条件测试

**场景1：延迟消息即将触发时取消**
- 在延迟消息触发前几秒取消订单
- 验证取消操作的时效性

**场景2：重复取消**
- 对同一订单多次调用取消接口
- 验证幂等性

**场景3：不存在的订单**
- 尝试取消不存在的订单ID
- 验证错误处理

## 性能测试

### 1. 延迟消息取消性能
```bash
# 创建1000个延迟消息
for i in {1..1000}; do
  curl -X POST http://localhost:8082/delayed-services/api/add \
    -H "Content-Type: application/json" \
    -d "{\"id\":\"test_$i\",\"payloadJson\":\"{\\\"orderId\\\":\\\"ORDER_$i\\\"}\",\"triggerTime\":$(($(date +%s)*1000+300000)),\"targetExchange\":\"test\",\"targetRoutingKey\":\"test\"}"
done

# 批量取消测试
time for i in {1..100}; do
  curl -X POST http://localhost:8082/delayed-services/api/cancel/ORDER_$i
done
```

### 2. 内存使用监控
```bash
# 监控Redis内存使用
redis-cli INFO memory | grep used_memory_human

# 监控延迟队列大小变化
watch -n 1 'redis-cli ZCARD delayed_queue:zset'
```

## 日志验证

### 关键日志检查点

**1. 延迟消息添加日志**
```
[order-services] 发送延迟消息: {"orderId":"ORDER_123"}, 触发时间=*************
[delayed-services] 添加延迟消息到队列: messageId=MSG_123, orderId=ORDER_123
```

**2. 延迟消息取消日志**
```
[order-services] 取消延迟消息: orderId=ORDER_123
[delayed-services] 成功取消延迟消息: messageId=MSG_123, orderId=ORDER_123
```

**3. 定时任务扫描日志**
```
[delayed-services] 发现 0 条到期延迟消息 (原本应该有1条，但已被取消)
```

## 监控指标

### 1. 业务指标
- 延迟消息创建数量
- 延迟消息取消数量  
- 延迟消息触发数量
- 库存释放成功率

### 2. 技术指标
- Redis ZSet操作延迟
- 延迟服务API响应时间
- 消息取消成功率
- 服务可用性

## 回滚方案

如果新方案出现问题，可以快速回滚：

### 1. 代码回滚
```bash
# 回滚到之前版本
git checkout <previous_commit>
# 重新部署服务
```

### 2. 配置回滚
```bash
# 临时禁用延迟消息取消功能
# 在OrderCancelledEventMessageLister中注释掉取消调用
// orderProducer.cancelDelayedMessage(event.getPrentOrderId());
```

### 3. 数据清理
```bash
# 清理测试数据
redis-cli DEL delayed_queue:zset
# 重启相关服务
```

## 成功标准

### 功能验证 ✅
- [ ] 订单取消时延迟消息被正确删除
- [ ] 延迟消息不存在时取消操作不报错
- [ ] 并发场景下不会误删其他订单的延迟消息
- [ ] 延迟服务不可用时有合理的降级处理

### 性能验证 ✅  
- [ ] 延迟消息取消操作在100ms内完成
- [ ] 1000个延迟消息的批量取消在10秒内完成
- [ ] Redis内存使用合理，无内存泄漏

### 稳定性验证 ✅
- [ ] 连续运行24小时无异常
- [ ] 服务重启后功能正常
- [ ] 网络抖动时有重试机制

## 上线计划

### 1. 灰度发布
- 先在测试环境充分验证
- 选择10%的订单启用新功能
- 观察1周无问题后全量发布

### 2. 监控告警
- 设置延迟消息取消失败告警
- 设置Redis队列异常增长告警
- 设置服务可用性告警

### 3. 文档更新
- 更新运维手册
- 更新故障排查指南
- 培训相关人员
