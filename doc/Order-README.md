# B2B 电商父子订单状态流转图

该流程图完整展示了父订单和子订单在 B2B 电商系统中的状态流转关系，包含支付、发货、售后等核心业务流程：

```mermaid
graph TB
    %% 父订单状态流转
    subgraph 父订单状态
        PG_PENDING_PAYMENT(待支付) --> |支付发起| PG_IN_PAYMENT(支付中)
        PG_IN_PAYMENT --> |支付成功| PG_PAID(已支付)
        PG_IN_PAYMENT --> |支付失败| PG_PENDING_PAYMENT
        
        PG_PAID --> |准备发货| PG_PENDING_SHIPMENT(待发货)
        PG_PENDING_SHIPMENT --> |全部发货| PG_SHIPPED(已发货)
        PG_PENDING_SHIPMENT --> |部分发货| PG_PARTIALLY_SHIPPED(部分发货)
        PG_PARTIALLY_SHIPPED --> |剩余发货| PG_SHIPPED
        
        PG_SHIPPED --> |物流签收| PG_DELIVERED(已送达)
        PG_DELIVERED --> |确认完成| PG_COMPLETED(已完成)
        
        %% 售后路径
        PG_PAID --> |申请退款| PG_REFUNDING(退款中)
        PG_PENDING_SHIPMENT --> |申请退款| PG_REFUNDING
        PG_REFUNDING --> |退款完成| PG_PARTIALLY_CANCELLED(部分取消)
        PG_REFUNDING --> |全额退款| PG_CANCELLED(已取消)
        
        PG_SHIPPED --> |申请退货| PG_RETURNING(退货中)
        PG_DELIVERED --> |申请退货| PG_RETURNING
        PG_RETURNING --> |退货完成| PG_PARTIALLY_CANCELLED
        PG_RETURNING --> |全部退货| PG_CANCELLED
        
        %% 取消路径
        PG_PENDING_PAYMENT --> |订单取消| PG_CANCELLED
        PG_IN_PAYMENT --> |订单取消| PG_CANCELLED
        PG_PARTIALLY_SHIPPED --> |剩余取消| PG_PARTIALLY_CANCELLED
    end
    
    %% 子订单状态流转
    subgraph 子订单状态
        PI_PENDING_PAYMENT(待支付) --> |支付成功| PI_PAID(已支付)
        PI_PAID --> |发货准备| PI_PENDING_SHIPMENT(待发货)
        PI_PENDING_SHIPMENT --> |发货完成| PI_SHIPPED(已发货)
        PI_SHIPPED --> |物流签收| PI_DELIVERED(已送达)
        PI_DELIVERED --> |确认完成| PI_COMPLETED(已完成)
        
        %% 取消路径
        PI_PENDING_PAYMENT --> |订单取消| PI_CANCELLED(已取消)
        PI_PAID --> |订单取消| PI_CANCELLED
        PI_PENDING_SHIPMENT --> |订单取消| PI_CANCELLED
    end
    
    %% 父子订单状态关联
    PI_PAID --> |所有子订单支付成功| PG_PAID
    PI_SHIPPED --> |子订单发货触发| PG_PARTIALLY_SHIPPED
    PI_CANCELLED --> |子订单取消触发| PG_PARTIALLY_CANCELLED
    PI_CANCELLED --> |所有子订单取消| PG_CANCELLED 
    
```

## 订单超时处理机制

### 延迟消息管理

系统采用延迟消息机制处理订单超时，支持消息取消以避免重复处理：

```mermaid
graph TB
    subgraph "订单创建流程"
        A[用户创建订单] --> B[订单服务创建订单]
        B --> C[发送延迟消息到Redis ZSet]
        C --> D[商品服务锁定库存]
    end

    subgraph "订单取消流程"
        E[用户手动取消订单] --> F[订单服务处理取消]
        F --> G[发送订单取消事件]
        G --> H[商品服务释放库存]
        G --> I[调用延迟服务取消消息]
        I --> J[从Redis ZSet删除延迟消息]
    end

    subgraph "延迟消息处理"
        K[定时任务扫描延迟队列] --> L{延迟消息是否存在?}
        L -->|存在| M[发送超时消息到MQ]
        L -->|不存在已被取消| N[跳过处理]
        M --> O[各服务处理订单超时]
    end

    style A fill:#e1f5fe
    style E fill:#fff3e0
    style J fill:#c8e6c9
    style N fill:#c8e6c9
```

### 核心机制

#### 1. 延迟消息创建
- 订单创建时自动发送5分钟延迟消息
- 消息存储在Redis ZSet中，以触发时间为score
- 消息体包含订单ID、用户ID等关键信息

#### 2. 延迟消息取消
- 订单手动取消时，同时取消对应的延迟消息
- 通过订单ID匹配并删除Redis中的延迟消息
- 防止已取消订单的延迟消息触发错误处理

#### 3. 超时处理
- 定时任务扫描到期的延迟消息
- 只处理仍存在于队列中的消息
- 已取消的订单不会触发超时处理

### 技术实现

#### 延迟服务接口
```java
// 添加延迟消息
POST /delayed-services/api/add

// 取消延迟消息
POST /delayed-services/api/cancel/{orderId}
```

#### 关键组件
- **DelayedMessage**: 延迟消息实体
- **RedisDelayedQueueService**: 延迟队列管理服务
- **OrderCancelledEventListener**: 订单取消事件监听器
- **SendMessageJob**: 延迟消息扫描定时任务

### 优势特点

1. **数据一致性**: 避免延迟消息释放其他订单的库存
2. **高性能**: 基于Redis的高效操作
3. **可靠性**: 完善的错误处理和降级机制
4. **兼容性**: 不影响现有业务逻辑

### 监控指标

- 延迟消息创建/取消数量
- 延迟消息处理成功率
- Redis队列大小变化
- 服务响应时间