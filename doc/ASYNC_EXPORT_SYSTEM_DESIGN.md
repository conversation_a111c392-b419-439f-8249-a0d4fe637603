# 异步文件导出系统设计方案

## 目录

- [1. 系统概述](#1-系统概述)
- [2. 架构设计](#2-架构设计)
- [3. 核心组件](#3-核心组件)
- [4. 实现细节](#4-实现细节)
- [5. 部署配置](#5-部署配置)
- [6. 监控告警](#6-监控告警)
- [7. 扩展计划](#7-扩展计划)

## 1. 系统概述

### 1.1 业务背景

在微服务架构中，用户经常需要导出大量数据（如订单列表、用户数据等），同步导出容易导致系统超时和资源占用过高。因此设计一套异步文件导出系统，通过消息队列实现异步处理，提升用户体验和系统稳定性。

### 1.2 设计目标

- **异步处理**: 用户提交导出请求后立即返回，后台异步处理
- **高可用**: 支持任务失败重试机制，确保导出成功率
- **资源控制**: 限制并发导出任务数量，避免系统OOM
- **扩展性**: 支持多种导出类型，易于扩展新的导出策略
- **用户友好**: 提供导出进度查询和文件下载功能

### 1.3 技术栈

- **消息队列**: RabbitMQ (项目现有基础设施)
- **导出组件**: EasyExcel
- **分布式锁**: Redis (限制并发数量)
- **任务调度**: XXL-Job (文件清理)
- **限流组件**: 自定义注解 + Redis

## 2. 架构设计

### 2.1 系统架构图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   用户端    │    │   网关层    │    │  业务服务   │
│             │◄──►│             │◄──►│             │
│ 导出请求    │    │  路由转发   │    │ 任务创建    │
│ 进度查询    │    │  鉴权限流   │    │ 文件下载    │
│ 文件下载    │    │             │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  文件存储   │    │  消息队列   │    │  数据库层   │
│             │    │             │    │             │
│ 导出文件    │◄──►│ 任务队列    │◄──►│ 任务状态    │
│ 临时存储    │    │ 重试队列    │    │ 用户信息    │
│ 定期清理    │    │ 死信队列    │    │ 业务数据    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 2.2 数据流程

1. 用户发起导出请求 → 创建导出任务记录
2. 发送消息到RabbitMQ → 异步处理队列
3. 消费者处理任务 → 调用对应导出策略
4. 生成Excel文件 → 更新任务状态
5. 用户查询进度 → 下载完成的文件

## 3. 核心组件

### 3.1 数据库设计

#### 导出任务表 (export_task)

```sql
CREATE TABLE `export_task`
(
    `id`            bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id`       varchar(64) NOT NULL COMMENT '任务唯一标识',
    `user_id`       varchar(64) NOT NULL COMMENT '用户ID',
    `export_type`   varchar(32) NOT NULL COMMENT '导出类型',
    `file_name`     varchar(255)         DEFAULT NULL COMMENT '文件名',
    `file_path`     varchar(512)         DEFAULT NULL COMMENT '文件路径',
    `status`        tinyint     NOT NULL DEFAULT '0' COMMENT '任务状态:0-待处理,1-处理中,2-成功,3-失败',
    `params`        text COMMENT '导出参数JSON',
    `retry_count`   int         NOT NULL DEFAULT '0' COMMENT '重试次数',
    `error_message` text COMMENT '错误信息',
    `create_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `expire_time`   datetime    NOT NULL COMMENT '文件过期时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    KEY `idx_user_id_status` (`user_id`, `status`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='导出任务表';
```

### 3.2 核心实体类

```java
/**
 * 导出任务实体
 */
@Entity
@Table(name = "export_task")
@Data
public class ExportTask {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id", unique = true, nullable = false)
    private String taskId;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "export_type", nullable = false)
    private String exportType;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "status", nullable = false)
    private Integer status; // 0-待处理,1-处理中,2-成功,3-失败

    @Column(name = "params", columnDefinition = "TEXT")
    private String params;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    @Column(name = "expire_time", nullable = false)
    private LocalDateTime expireTime;
}

/**
 * 导出任务消息
 */
@Data
public class ExportTaskMessage {
    private String taskId;
    private String userId;
    private String exportType;
    private String params;
    private Integer retryCount = 0;
}

/**
 * 创建导出任务请求
 */
@Data
@Valid
public class CreateExportTaskRequest {
    @NotBlank(message = "导出类型不能为空")
    private String exportType;

    @NotNull(message = "导出参数不能为空")
    private Map<String, Object> params;
}

/**
 * 订单导出参数
 */
@Data
public class OrderExportParams {
    private String startDate;
    private String endDate;
    private Integer status;
    private String merchantId;
    private String orderNo;
}
```

### 3.3 导出策略接口

```java
/**
 * 导出策略接口
 */
public interface ExportStrategy {
    /**
     * 导出数据
     * @param params 导出参数JSON字符串
     * @param filePath 文件保存路径
     * @throws Exception 导出异常
     */
    void export(String params, String filePath) throws Exception;

    /**
     * 获取策略类型
     * @return 策略类型标识
     */
    String getType();

    /**
     * 获取导出文件名前缀
     * @return 文件名前缀
     */
    default String getFileNamePrefix() {
        return getType().toLowerCase();
    }
}

/**
 * 策略工厂类
 */
@Component
public class ExportStrategyFactory {

    @Autowired
    private Map<String, ExportStrategy> strategyMap;

    /**
     * 获取导出策略
     * @param exportType 导出类型
     * @return 导出策略实例
     */
    public ExportStrategy getStrategy(String exportType) {
        ExportStrategy strategy = strategyMap.get(exportType);
        if (strategy == null) {
            throw new BusinessException("不支持的导出类型: " + exportType);
        }
        return strategy;
    }

    /**
     * 获取所有支持的导出类型
     * @return 导出类型列表
     */
    public Set<String> getSupportedTypes() {
        return strategyMap.keySet();
    }
}
```

### 3.4 订单导出策略实现

```java
/**
 * 订单导出策略实现
 */
@Component("ORDER_LIST")
@Slf4j
public class OrderExportStrategy implements ExportStrategy {

    @Autowired
    private OrderMapper orderMapper;

    private static final int BATCH_SIZE = 1000; // 批次大小

    @Override
    public void export(String params, String filePath) throws Exception {
        log.info("开始导出订单数据，参数: {}, 文件路径: {}", params, filePath);

        // 解析导出参数
        OrderExportParams exportParams = JSON.parseObject(params, OrderExportParams.class);

        // 创建Excel写入器
        try (ExcelWriter excelWriter = EasyExcel.write(filePath, OrderExportVO.class)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build()) {

            WriteSheet writeSheet = EasyExcel.writerSheet("订单列表").build();

            int pageNum = 1;
            List<OrderExportVO> dataList;
            int totalCount = 0;

            do {
                // 分页查询数据，避免内存溢出
                dataList = orderMapper.selectOrderForExport(exportParams, pageNum, BATCH_SIZE);

                if (!dataList.isEmpty()) {
                    excelWriter.write(dataList, writeSheet);
                    totalCount += dataList.size();
                    log.info("已导出订单数据: {} 条", totalCount);
                }

                pageNum++;

                // 避免无限循环
                if (pageNum > 10000) {
                    throw new BusinessException("导出数据量过大，请缩小查询范围");
                }

            } while (dataList.size() == BATCH_SIZE);

            log.info("订单导出完成，总计: {} 条", totalCount);
        }
    }

    @Override
    public String getType() {
        return "ORDER_LIST";
    }
}

/**
 * 订单导出VO
 */
@Data
@ExcelTarget("OrderExportVO")
public class OrderExportVO {
    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("商户名称")
    private String merchantName;

    @ExcelProperty("订单金额")
    private BigDecimal amount;

    @ExcelProperty("订单状态")
    private String statusName;

    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ExcelProperty("支付时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @ExcelProperty("用户手机号")
    @ExcelProperty(converter = PhoneDesensitizationConverter.class)
    private String userPhone;
}
```

## 4. 实现细节

### 4.1 核心服务类

```java
/**
 * 导出任务服务
 */
@Service
@Slf4j
public class ExportTaskService {

    @Autowired
    private ExportTaskMapper exportTaskMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ExportStrategyFactory strategyFactory;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${app.export.max-concurrent-tasks:5}")
    private int maxConcurrentTasks;

    @Value("${app.export.file-expire-days:7}")
    private int fileExpireDays;

    /**
     * 创建导出任务
     */
    @DistributedLock(key = "'export:user:' + #userId", expire = 60)
    public ResultVo createExportTask(String userId, String exportType, String params) {
        // 验证导出类型
        if (!strategyFactory.getSupportedTypes().contains(exportType)) {
            return ResultVo.fail("不支持的导出类型: " + exportType);
        }

        // 检查用户当前进行中的任务数量
        int runningCount = exportTaskMapper.countRunningTasksByUser(userId);
        if (runningCount >= maxConcurrentTasks) {
            return ResultVo.fail("当前有太多导出任务正在进行，请稍后再试");
        }

        // 检查全局并发限制
        String globalLockKey = "export:global:count";
        Long globalCount = redisTemplate.opsForValue().increment(globalLockKey, 1);
        redisTemplate.expire(globalLockKey, Duration.ofMinutes(1));

        if (globalCount > 50) { // 全局最大50个并发任务
            return ResultVo.fail("系统繁忙，请稍后再试");
        }

        // 创建任务记录
        ExportTask task = new ExportTask();
        task.setTaskId(generateTaskId());
        task.setUserId(userId);
        task.setExportType(exportType);
        task.setParams(params);
        task.setStatus(0); // 待处理
        task.setRetryCount(0);
        task.setCreateTime(LocalDateTime.now());
        task.setExpireTime(LocalDateTime.now().plusDays(fileExpireDays));

        exportTaskMapper.insert(task);

        // 发送异步消息
        ExportTaskMessage message = new ExportTaskMessage();
        message.setTaskId(task.getTaskId());
        message.setUserId(userId);
        message.setExportType(exportType);
        message.setParams(params);

        rabbitTemplate.convertAndSend(
                ExportRabbitConfig.EXPORT_EXCHANGE,
                "export.task",
                message
        );

        log.info("创建导出任务成功，taskId: {}, userId: {}, type: {}",
                task.getTaskId(), userId, exportType);

        return ResultVo.success(Map.of(
                "taskId", task.getTaskId(),
                "message", "导出任务已创建，请稍后查看进度"
        ));
    }

    /**
     * 获取用户导出任务列表
     */
    public ResultVo getUserExportTasks(String userId, Integer pageNum, Integer pageSize) {
        Page<ExportTask> page = new Page<>(pageNum, pageSize);
        Page<ExportTask> result = exportTaskMapper.selectUserTasks(page, userId);

        // 转换为VO
        List<ExportTaskVO> voList = result.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        PageVO<ExportTaskVO> pageVO = new PageVO<>();
        pageVO.setRecords(voList);
        pageVO.setTotal(result.getTotal());
        pageVO.setCurrent(result.getCurrent());
        pageVO.setSize(result.getSize());

        return ResultVo.success(pageVO);
    }

    /**
     * 下载导出文件
     */
    public ResponseEntity<Resource> downloadFile(String userId, String taskId) {
        ExportTask task = exportTaskMapper.selectByTaskIdAndUserId(taskId, userId);

        if (task == null) {
            throw new BusinessException("导出任务不存在");
        }

        if (task.getStatus() != 2) {
            throw new BusinessException("文件尚未生成或生成失败");
        }

        if (task.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("文件已过期");
        }

        try {
            Path filePath = Paths.get(task.getFilePath());
            if (!Files.exists(filePath)) {
                throw new BusinessException("文件不存在");
            }

            Resource resource = new UrlResource(filePath.toUri());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + URLEncoder.encode(task.getFileName(), "UTF-8") + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(resource);

        } catch (Exception e) {
            log.error("文件下载失败，taskId: {}", taskId, e);
            throw new BusinessException("文件下载失败");
        }
    }

    private String generateTaskId() {
        return "EXPORT_" + System.currentTimeMillis() + "_" + RandomUtil.randomString(6);
    }

    private ExportTaskVO convertToVO(ExportTask task) {
        ExportTaskVO vo = new ExportTaskVO();
        BeanUtils.copyProperties(task, vo);
        vo.setStatusName(getStatusName(task.getStatus()));
        vo.setCanDownload(task.getStatus() == 2 &&
                task.getExpireTime().isAfter(LocalDateTime.now()));
        return vo;
    }

    private String getStatusName(Integer status) {
        switch (status) {
            case 0:
                return "等待中";
            case 1:
                return "处理中";
            case 2:
                return "已完成";
            case 3:
                return "失败";
            default:
                return "未知";
        }
    }
}
```

### 4.2 RabbitMQ配置

```java
/**
 * 导出任务RabbitMQ配置
 */
@Configuration
@EnableRabbit
public class ExportRabbitConfig {

    public static final String EXPORT_EXCHANGE = "export.exchange";
    public static final String EXPORT_QUEUE = "export.task.queue";
    public static final String EXPORT_RETRY_QUEUE = "export.retry.queue";
    public static final String EXPORT_DEAD_QUEUE = "export.dead.queue";

    @Bean
    public TopicExchange exportExchange() {
        return ExchangeBuilder.topicExchange(EXPORT_EXCHANGE)
                .durable(true)
                .build();
    }

    @Bean
    public Queue exportQueue() {
        return QueueBuilder.durable(EXPORT_QUEUE)
                .withArgument("x-dead-letter-exchange", EXPORT_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", "export.dead")
                .withArgument("x-max-length", 10000) // 队列最大长度
                .build();
    }

    @Bean
    public Queue retryQueue() {
        return QueueBuilder.durable(EXPORT_RETRY_QUEUE)
                .withArgument("x-message-ttl", 60000) // 1分钟延迟重试
                .withArgument("x-dead-letter-exchange", EXPORT_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", "export.task")
                .build();
    }

    @Bean
    public Queue deadQueue() {
        return QueueBuilder.durable(EXPORT_DEAD_QUEUE).build();
    }

    @Bean
    public Binding exportBinding() {
        return BindingBuilder.bind(exportQueue())
                .to(exportExchange())
                .with("export.task");
    }

    @Bean
    public Binding retryBinding() {
        return BindingBuilder.bind(retryQueue())
                .to(exportExchange())
                .with("export.retry");
    }

    @Bean
    public Binding deadBinding() {
        return BindingBuilder.bind(deadQueue())
                .to(exportExchange())
                .with("export.dead");
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (!ack) {
                log.error("消息发送失败: {}", cause);
            }
        });
        return template;
    }
}
```

### 4.3 消息监听器

```java
/**
 * 导出任务消息监听器
 */
@Component
@Slf4j
public class ExportTaskListener {

    @Autowired
    private ExportTaskMapper exportTaskMapper;

    @Autowired
    private ExportStrategyFactory strategyFactory;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Value("${app.export.file-path:/tmp/export/}")
    private String exportBasePath;

    private static final int MAX_RETRY_COUNT = 3;

    @RabbitListener(
            queues = ExportRabbitConfig.EXPORT_QUEUE,
            concurrency = "3-5", // 控制并发消费者数量
            ackMode = "MANUAL"
    )
    @RateLimiter(key = "export:global", rate = 10, period = 60)
    public void handleExportTask(ExportTaskMessage message, Channel channel,
                                 @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String taskId = message.getTaskId();
        log.info("开始处理导出任务: {}", taskId);

        try {
            ExportTask task = exportTaskMapper.selectByTaskId(taskId);
            if (task == null) {
                log.error("导出任务不存在: {}", taskId);
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 检查任务状态，避免重复处理
            if (task.getStatus() != 0) {
                log.warn("任务状态异常，跳过处理: {}, status: {}", taskId, task.getStatus());
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 更新任务状态为处理中
            updateTaskStatus(task, 1, null);

            // 执行导出
            processExportTask(task);

            // 确认消息
            channel.basicAck(deliveryTag, false);
            log.info("导出任务处理完成: {}", taskId);

        } catch (Exception e) {
            log.error("导出任务处理失败: {}", taskId, e);
            try {
                handleTaskFailure(message, e.getMessage());
                channel.basicAck(deliveryTag, false);
            } catch (Exception ex) {
                log.error("处理任务失败时出错: {}", taskId, ex);
                try {
                    channel.basicNack(deliveryTag, false, false);
                } catch (IOException ioEx) {
                    log.error("消息nack失败: {}", taskId, ioEx);
                }
            }
        }
    }

    private void processExportTask(ExportTask task) throws Exception {
        // 获取导出策略
        ExportStrategy strategy = strategyFactory.getStrategy(task.getExportType());

        // 生成文件路径
        String fileName = generateFileName(task.getExportType(), task.getUserId());
        String filePath = exportBasePath + fileName;

        // 确保目录存在
        Path directory = Paths.get(exportBasePath);
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }

        // 执行导出
        strategy.export(task.getParams(), filePath);

        // 验证文件是否生成成功
        if (!Files.exists(Paths.get(filePath))) {
            throw new BusinessException("文件生成失败");
        }

        // 更新任务状态为成功
        task.setFileName(fileName);
        task.setFilePath(filePath);
        updateTaskStatus(task, 2, null);
    }

    private void handleTaskFailure(ExportTaskMessage message, String errorMessage) {
        ExportTask task = exportTaskMapper.selectByTaskId(message.getTaskId());
        if (task == null) {
            return;
        }

        task.setRetryCount(task.getRetryCount() + 1);
        task.setErrorMessage(errorMessage);

        if (task.getRetryCount() <= MAX_RETRY_COUNT) {
            // 重试次数未超限，发送到重试队列
            updateTaskStatus(task, 0, errorMessage);

            // 延迟重试
            message.setRetryCount(task.getRetryCount());
            rabbitTemplate.convertAndSend(
                    ExportRabbitConfig.EXPORT_EXCHANGE,
                    "export.retry",
                    message
            );
            log.info("导出任务进入重试队列: {}, 重试次数: {}",
                    task.getTaskId(), task.getRetryCount());
        } else {
            // 超过最大重试次数，标记为失败
            updateTaskStatus(task, 3, errorMessage);
            log.error("导出任务最终失败: {}", task.getTaskId());
        }
    }

    private void updateTaskStatus(ExportTask task, Integer status, String errorMessage) {
        task.setStatus(status);
        task.setErrorMessage(errorMessage);
        task.setUpdateTime(LocalDateTime.now());
        exportTaskMapper.updateById(task);
    }

    private String generateFileName(String exportType, String userId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String prefix = exportType.toLowerCase().replace("_", "-");
        return String.format("%s-%s-%s.xlsx", prefix, userId, timestamp);
    }
}
```

### 4.4 控制器接口

```java
/**
 * 导出任务控制器
 */
@RestController
@RequestMapping("/api/export")
@Tag(name = "导出任务管理", description = "异步文件导出相关接口")
@Slf4j
public class ExportController {

    @Autowired
    private ExportTaskService exportTaskService;

    /**
     * 创建导出任务
     */
    @PostMapping("/task")
    @Operation(summary = "创建导出任务", description = "提交异步导出请求")
    @PermissionCheck(permission = "user")
    @Loggable(value = "创建导出任务")
    @RateLimiter(capacity = 10, refillRate = 5, message = "导出请求过于频繁")
    public HttpResult<Map<String, Object>> createExportTask(
            @Valid @RequestBody CreateExportTaskRequest request,
            HttpServletRequest httpRequest) {

        String userId = JwtUtils.getUserId(httpRequest);
        ResultVo result = exportTaskService.createExportTask(
                userId,
                request.getExportType(),
                JSON.toJSONString(request.getParams())
        );

        if (result.isSuccess()) {
            return HttpResult.ok(result.getData());
        } else {
            return HttpResult.fail(result.getMessage());
        }
    }

    /**
     * 获取用户导出任务列表
     */
    @GetMapping("/tasks")
    @Operation(summary = "查询导出任务列表", description = "获取当前用户的导出任务列表")
    @PermissionCheck(permission = "user")
    public HttpResult<PageVO<ExportTaskVO>> getUserExportTasks(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request) {

        String userId = JwtUtils.getUserId(request);
        ResultVo result = exportTaskService.getUserExportTasks(userId, pageNum, pageSize);

        if (result.isSuccess()) {
            return HttpResult.ok(result.getData());
        } else {
            return HttpResult.fail(result.getMessage());
        }
    }

    /**
     * 下载导出文件
     */
    @GetMapping("/download/{taskId}")
    @Operation(summary = "下载导出文件", description = "下载指定任务的导出文件")
    @PermissionCheck(permission = "user")
    @Loggable(value = "下载导出文件")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable String taskId,
            HttpServletRequest request) {

        String userId = JwtUtils.getUserId(request);
        return exportTaskService.downloadFile(userId, taskId);
    }

    /**
     * 获取支持的导出类型
     */
    @GetMapping("/types")
    @Operation(summary = "获取导出类型", description = "获取系统支持的所有导出类型")
    @PermissionCheck(permission = "user")
    public HttpResult<Set<String>> getSupportedExportTypes() {
        // 这里可以返回更详细的类型信息
        Map<String, String> typeMap = Map.of(
                "ORDER_LIST", "订单列表导出",
                "USER_LIST", "用户列表导出",
                "PRODUCT_LIST", "商品列表导出"
        );
        return HttpResult.ok(typeMap);
    }
}
```

### 4.5 定时任务 - 文件清理

```java
/**
 * 导出文件清理任务
 */
@Component
@Slf4j
public class ExportFileCleanupJob {

    @Autowired
    private ExportTaskMapper exportTaskMapper;

    /**
     * 清理过期文件
     */
    @XxlJob("exportFileCleanup")
    public void cleanupExpiredFiles() {
        log.info("开始清理过期导出文件");

        try {
            List<ExportTask> expiredTasks = exportTaskMapper.selectExpiredTasks();
            log.info("找到过期任务数量: {}", expiredTasks.size());

            int successCount = 0;
            int failCount = 0;

            for (ExportTask task : expiredTasks) {
                try {
                    // 删除物理文件
                    if (StringUtils.isNotBlank(task.getFilePath())) {
                        Path filePath = Paths.get(task.getFilePath());
                        if (Files.exists(filePath)) {
                            Files.delete(filePath);
                            log.debug("删除过期文件: {}", task.getFilePath());
                        }
                    }

                    // 删除数据库记录
                    exportTaskMapper.deleteById(task.getId());
                    successCount++;

                } catch (Exception e) {
                    log.error("清理过期文件失败，taskId: {}", task.getTaskId(), e);
                    failCount++;
                }
            }

            log.info("过期文件清理完成，成功: {}, 失败: {}", successCount, failCount);

        } catch (Exception e) {
            log.error("清理过期文件任务执行失败", e);
        }
    }

    /**
     * 清理失败任务
     */
    @XxlJob("cleanupFailedTasks")
    public void cleanupFailedTasks() {
        log.info("开始清理长期失败的导出任务");

        try {
            // 清理7天前的失败任务
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
            int deletedCount = exportTaskMapper.deleteFailedTasksBefore(cutoffTime);

            log.info("清理长期失败任务完成，删除数量: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理失败任务执行失败", e);
        }
    }
}
```

## 5. 部署配置

### 5.1 application.yml配置

```yaml
# 导出系统配置
app:
  export:
    # 文件存储路径
    file-path: /data/export/
    # 用户最大并发任务数
    max-concurrent-tasks: 5
    # 文件过期天数
    file-expire-days: 7
    # 支持的导出类型
    supported-types:
      - ORDER_LIST
      - USER_LIST
      - PRODUCT_LIST

# RabbitMQ配置
spring:
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: ${RABBITMQ_VHOST:/}
    listener:
      simple:
        concurrency: 3
        max-concurrency: 10
        acknowledge-mode: manual
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000ms

# XXL-Job配置
xxl:
  job:
    admin:
      addresses: ${XXL_JOB_ADMIN_ADDRESSES:http://localhost:8080/xxl-job-admin}
    executor:
      appname: knet-export-job
      address:
      ip:
      port: 9999
      logpath: /data/logs/xxl-job/
      logretentiondays: 30
```

### 5.2 Docker部署配置

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

# 创建导出文件目录
RUN mkdir -p /data/export && chmod 755 /data/export

# 复制应用程序
COPY target/goods-services-*.jar /app/app.jar

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV EXPORT_FILE_PATH="/data/export/"

# 暴露端口
EXPOSE 8080

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  goods-service:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - export-data:/data/export
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - EXPORT_FILE_PATH=/data/export/
    depends_on:
      - rabbitmq
      - redis
      - mysql
    networks:
      - knet-network

volumes:
  export-data:
    driver: local

networks:
  knet-network:
    external: true
```

## 6. 监控告警

### 6.1 监控指标

```java
/**
 * 导出系统监控指标
 */
@Component
public class ExportMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter taskCreatedCounter;
    private final Counter taskSuccessCounter;
    private final Counter taskFailureCounter;
    private final Timer exportTimer;
    private final Gauge activeTasksGauge;

    @Autowired
    private ExportTaskMapper exportTaskMapper;

    public ExportMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.taskCreatedCounter = Counter.builder("export.task.created")
                .description("Created export tasks count")
                .register(meterRegistry);
        this.taskSuccessCounter = Counter.builder("export.task.success")
                .description("Successful export tasks count")
                .register(meterRegistry);
        this.taskFailureCounter = Counter.builder("export.task.failure")
                .description("Failed export tasks count")
                .register(meterRegistry);
        this.exportTimer = Timer.builder("export.task.duration")
                .description("Export task execution time")
                .register(meterRegistry);
        this.activeTasksGauge = Gauge.builder("export.task.active")
                .description("Active export tasks count")
                .register(meterRegistry, this, ExportMetrics::getActiveTasksCount);
    }

    public void incrementTaskCreated(String exportType) {
        taskCreatedCounter.increment(Tags.of("type", exportType));
    }

    public void incrementTaskSuccess(String exportType) {
        taskSuccessCounter.increment(Tags.of("type", exportType));
    }

    public void incrementTaskFailure(String exportType, String errorType) {
        taskFailureCounter.increment(Tags.of("type", exportType, "error", errorType));
    }

    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }

    private double getActiveTasksCount() {
        return exportTaskMapper.countRunningTasks();
    }
}
```

### 6.2 告警规则

```yaml
# prometheus.yml
rule_files:
  - "export_alert_rules.yml"

# export_alert_rules.yml
groups:
  - name: export_system_alerts
    rules:
      - alert: ExportTaskFailureRateHigh
        expr: rate(export_task_failure_total[5m]) / rate(export_task_created_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "导出任务失败率过高"
          description: "导出任务失败率超过10%，当前值: {{ $value }}"

      - alert: ExportTaskQueueTooLong
        expr: rabbitmq_queue_messages{queue="export.task.queue"} > 1000
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "导出任务队列积压严重"
          description: "导出任务队列消息数量: {{ $value }}"

      - alert: ExportTaskExecutionTimeHigh
        expr: histogram_quantile(0.95, rate(export_task_duration_seconds_bucket[5m])) > 300
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "导出任务执行时间过长"
          description: "95%的导出任务执行时间超过5分钟"
```

## 7. 扩展计划

### 7.1 功能扩展

1. **多格式支持**: 支持PDF、CSV等多种导出格式
2. **模板定制**: 支持用户自定义导出模板
3. **增量导出**: 支持基于时间戳的增量数据导出
4. **导出预览**: 提供导出数据预览功能
5. **批量操作**: 支持批量删除和重新导出

### 7.2 性能优化

1. **分片导出**: 大数据量自动分片导出
2. **压缩支持**: 自动压缩大文件
3. **CDN集成**: 导出文件上传至CDN
4. **缓存优化**: 常用查询结果缓存

### 7.3 架构升级

1. **微服务拆分**: 独立导出服务
2. **容器化部署**: Kubernetes部署支持
3. **弹性伸缩**: 基于队列长度自动扩容
4. **多区域部署**: 支持多地域文件存储

## 总结

该异步文件导出系统充分利用了项目现有的技术栈，通过消息队列实现异步处理，有效解决了大数据量导出的性能问题。系统具备完善的错误处理、重试机制和监控告警，确保导出任务的可靠执行。

关键优势：

- **高可靠性**: 完善的重试和错误处理机制
- **高性能**: 分页查询和并发控制避免系统压力
- **易扩展**: 策略模式支持多种导出类型
- **用户友好**: 异步处理提升用户体验
- **运维友好**: 完善的监控和告警机制

该系统已考虑到生产环境的各种场景，可以直接应用于实际项目中。
