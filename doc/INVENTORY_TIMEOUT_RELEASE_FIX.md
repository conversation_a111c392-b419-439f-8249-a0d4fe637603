# 订单超时库存释放问题修复方案

## 问题描述

当前订单取消释放库存的实现存在重大问题：

### 问题场景
1. **订单A创建**：锁定商品X（oneId=123）
2. **订单A取消**：在5分钟内手动取消，库存正常释放
3. **订单B创建**：锁定同一商品X（oneId=123）
4. **延迟消息触发**：订单A的5分钟延迟消息到达，错误释放了订单B锁定的商品

### 根本原因
`InventoryCompensationServiceImpl.releaseInventoryForItem()` 方法只根据SKU、价格、尺码和状态来释放库存，**没有验证这些库存是否真的属于当前订单**。

```java
// 问题代码：按时间倒序释放，可能释放其他订单的库存
LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
updateWrapper
    .eq(KnetProduct::getSku, sku)
    .eq(KnetProduct::getPrice, originalPriceCents)
    .eq(KnetProduct::getSpec, size)
    .eq(KnetProduct::getStatus, ProductStatus.LOCKED)
    .set(KnetProduct::getStatus, ProductStatus.ON_SALE)
    .last("ORDER BY create_time DESC LIMIT " + count);
```

## 解决方案

### 方案1：基于oneId的精确释放（已实现）

**核心思路**：只释放确实属于当前订单的库存，通过oneId进行精确匹配。

**实现要点**：
1. 通过订单服务获取订单项详情，获取oneId列表
2. 只释放指定oneId的库存，避免误释放其他订单的库存
3. 添加订单状态验证，防止重复处理

**代码改进**：
```java
// 获取该订单项对应的oneId列表
List<String> oneIdsToRelease = orderItemDetail.getData().stream()
    .filter(orderItem -> sku.equals(orderItem.getSku()) 
            && size.equals(orderItem.getSize())
            && priceStr.equals(orderItem.getPrice().toString())
            && StrUtil.isNotBlank(orderItem.getOneId()))
    .map(KnetSysOrderItem::getOneId)
    .distinct()
    .toList();

// 逐个释放指定的oneId库存
for (String oneId : oneIdsToRelease) {
    // 查询指定oneId的锁定商品并释放
}
```

### 方案2：延迟消息去重机制（推荐补充实现）

**核心思路**：在延迟消息处理前，检查订单是否已经被处理过。

**实现建议**：
1. 在Redis中记录已处理的订单超时事件
2. 延迟消息处理前先检查是否已处理
3. 设置合理的过期时间（如1小时）

```java
// 伪代码示例
String timeoutKey = "order:timeout:processed:" + orderId;
if (RedisCacheUtil.hasKey(timeoutKey)) {
    log.info("订单超时已处理过，跳过: orderId={}", orderId);
    return;
}
// 处理超时逻辑...
RedisCacheUtil.set(timeoutKey, "processed", 3600); // 1小时过期
```

### 方案3：订单状态验证（已实现）

**核心思路**：在处理延迟消息前，验证订单当前状态。

**实现要点**：
1. 只有待支付状态的订单才进行超时处理
2. 已取消、已支付的订单跳过处理
3. 避免重复处理同一订单

## 风险评估

### 低风险
- ✅ 基于oneId的精确释放：安全可靠，不会误释放其他订单库存
- ✅ 订单状态验证：防止重复处理，逻辑清晰

### 中风险
- ⚠️ 性能影响：需要额外的数据库查询获取oneId列表
- ⚠️ 依赖订单服务：需要确保订单服务接口稳定

### 建议
1. **监控告警**：添加库存释放失败的监控告警
2. **日志完善**：详细记录释放过程，便于问题排查
3. **测试验证**：充分测试各种边界情况

## 测试建议

### 测试用例
1. **正常超时场景**：订单创建5分钟后未支付，验证库存正确释放
2. **手动取消场景**：订单手动取消后，延迟消息到达时不应重复释放
3. **并发场景**：多个订单同时操作同一商品，验证库存释放的准确性
4. **异常场景**：订单服务不可用时的降级处理

### 验证方法
```sql
-- 验证库存状态
SELECT one_id, status, version, create_time, update_time 
FROM knet_product 
WHERE sku = 'TEST_SKU' AND spec = 'TEST_SIZE'
ORDER BY create_time DESC;

-- 验证订单项状态
SELECT item_id, parent_order_id, one_id, status 
FROM sys_order_item 
WHERE parent_order_id = 'TEST_ORDER_ID';
```

## 部署建议

1. **灰度发布**：先在测试环境充分验证
2. **监控观察**：部署后密切观察库存释放日志
3. **回滚准备**：准备快速回滚方案
4. **数据备份**：部署前备份相关数据表

## 后续优化

1. **性能优化**：考虑批量查询oneId，减少数据库访问
2. **缓存优化**：将订单项信息缓存到Redis，减少远程调用
3. **异步处理**：考虑将库存释放改为异步处理，提高响应速度
