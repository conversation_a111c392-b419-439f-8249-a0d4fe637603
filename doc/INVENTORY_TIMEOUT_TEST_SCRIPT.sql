-- 订单超时库存释放问题测试脚本
-- 用于验证修复方案的有效性

-- ========================================
-- 1. 测试数据准备
-- ========================================

-- 创建测试商品
INSERT INTO knet_product (id, one_id, sku, spec, price, status, version, source, listing_id, warehouse, create_time, update_time)
VALUES 
(1001, 'TEST_ONE_ID_001', 'TEST_SKU_001', 'M', 10000, 'ON_SALE', 1, 'test_user', 'LISTING_001', 'WH_001', NOW(), NOW()),
(1002, 'TEST_ONE_ID_002', 'TEST_SKU_001', 'M', 10000, 'ON_SALE', 1, 'test_user', 'LISTING_002', 'WH_001', NOW(), NOW()),
(1003, 'TEST_ONE_ID_003', 'TEST_SKU_001', 'M', 10000, 'ON_SALE', 1, 'test_user', 'LISTING_003', 'WH_001', NOW(), NOW());

-- 创建测试订单组
INSERT INTO sys_order_group (order_id, user_id, status, total_amount, create_time, update_time)
VALUES 
('TEST_ORDER_A', 1001, 'PENDING_PAYMENT', 10000, NOW(), NOW()),
('TEST_ORDER_B', 1002, 'PENDING_PAYMENT', 10000, NOW(), NOW());

-- 创建测试订单项
INSERT INTO sys_order_item (item_id, parent_order_id, sku, size, price, count, status, one_id, knet_listing_id, warehouse, source, create_time, update_time)
VALUES 
(2001, 'TEST_ORDER_A', 'TEST_SKU_001', 'M', 100.00, 1, 'PENDING_PAYMENT', 'TEST_ONE_ID_001', 'LISTING_001', 'WH_001', 'test_user', NOW(), NOW()),
(2002, 'TEST_ORDER_B', 'TEST_SKU_001', 'M', 100.00, 1, 'PENDING_PAYMENT', 'TEST_ONE_ID_002', 'LISTING_002', 'WH_001', 'test_user', NOW(), NOW());

-- ========================================
-- 2. 模拟问题场景
-- ========================================

-- 步骤1：锁定订单A的库存
UPDATE knet_product 
SET status = 'LOCKED', version = version + 1, update_time = NOW()
WHERE one_id = 'TEST_ONE_ID_001';

-- 步骤2：模拟订单A手动取消，释放库存
UPDATE knet_product 
SET status = 'ON_SALE', version = version + 1, update_time = NOW()
WHERE one_id = 'TEST_ONE_ID_001';

UPDATE sys_order_group 
SET status = 'CANCELLED', update_time = NOW()
WHERE order_id = 'TEST_ORDER_A';

UPDATE sys_order_item 
SET status = 'CANCELLED', update_time = NOW()
WHERE parent_order_id = 'TEST_ORDER_A';

-- 步骤3：锁定订单B的库存（使用同一商品）
UPDATE knet_product 
SET status = 'LOCKED', version = version + 1, update_time = NOW()
WHERE one_id = 'TEST_ONE_ID_001';

UPDATE sys_order_item 
SET one_id = 'TEST_ONE_ID_001'
WHERE parent_order_id = 'TEST_ORDER_B';

-- ========================================
-- 3. 验证查询
-- ========================================

-- 查看当前库存状态
SELECT 
    one_id,
    sku,
    spec,
    status,
    version,
    create_time,
    update_time
FROM knet_product 
WHERE sku = 'TEST_SKU_001'
ORDER BY create_time;

-- 查看订单状态
SELECT 
    order_id,
    user_id,
    status,
    create_time,
    update_time
FROM sys_order_group 
WHERE order_id IN ('TEST_ORDER_A', 'TEST_ORDER_B')
ORDER BY create_time;

-- 查看订单项状态
SELECT 
    item_id,
    parent_order_id,
    sku,
    size,
    status,
    one_id,
    create_time,
    update_time
FROM sys_order_item 
WHERE parent_order_id IN ('TEST_ORDER_A', 'TEST_ORDER_B')
ORDER BY create_time;

-- ========================================
-- 4. 测试修复后的行为
-- ========================================

-- 模拟延迟消息处理订单A超时（应该被跳过，因为订单已取消）
-- 这个查询应该返回空结果，因为订单A已经是CANCELLED状态
SELECT 'Order A timeout processing should be skipped' as test_case,
       CASE 
           WHEN status = 'CANCELLED' THEN 'PASS: Order already cancelled, timeout processing should be skipped'
           ELSE 'FAIL: Order status is not cancelled'
       END as result
FROM sys_order_group 
WHERE order_id = 'TEST_ORDER_A';

-- 验证订单B的库存不会被误释放
-- 这个查询应该显示TEST_ONE_ID_001仍然是LOCKED状态
SELECT 'Order B inventory should remain locked' as test_case,
       CASE 
           WHEN status = 'LOCKED' THEN 'PASS: Inventory remains locked for Order B'
           ELSE 'FAIL: Inventory was incorrectly released'
       END as result
FROM knet_product 
WHERE one_id = 'TEST_ONE_ID_001';

-- ========================================
-- 5. 清理测试数据
-- ========================================

-- 删除测试数据（谨慎执行）
-- DELETE FROM sys_order_item WHERE parent_order_id IN ('TEST_ORDER_A', 'TEST_ORDER_B');
-- DELETE FROM sys_order_group WHERE order_id IN ('TEST_ORDER_A', 'TEST_ORDER_B');
-- DELETE FROM knet_product WHERE one_id IN ('TEST_ONE_ID_001', 'TEST_ONE_ID_002', 'TEST_ONE_ID_003');

-- ========================================
-- 6. 性能测试查询
-- ========================================

-- 测试基于oneId的查询性能
EXPLAIN SELECT * FROM knet_product WHERE one_id = 'TEST_ONE_ID_001' AND status = 'LOCKED';

-- 测试批量oneId查询性能
EXPLAIN SELECT * FROM knet_product WHERE one_id IN ('TEST_ONE_ID_001', 'TEST_ONE_ID_002', 'TEST_ONE_ID_003') AND status = 'LOCKED';

-- 建议添加索引（如果不存在）
-- CREATE INDEX idx_knet_product_one_id_status ON knet_product(one_id, status);
-- CREATE INDEX idx_sys_order_item_parent_order_id_sku ON sys_order_item(parent_order_id, sku, size);

-- ========================================
-- 7. 监控查询
-- ========================================

-- 查询最近1小时内的库存释放操作
SELECT 
    one_id,
    sku,
    status,
    version,
    update_time,
    CASE 
        WHEN update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Recent'
        ELSE 'Old'
    END as recency
FROM knet_product 
WHERE update_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
  AND sku LIKE 'TEST_%'
ORDER BY update_time DESC;

-- 查询可能存在问题的订单（状态不一致）
SELECT 
    og.order_id,
    og.status as group_status,
    COUNT(oi.item_id) as item_count,
    COUNT(CASE WHEN oi.status = 'PENDING_PAYMENT' THEN 1 END) as pending_items,
    COUNT(CASE WHEN oi.status = 'CANCELLED' THEN 1 END) as cancelled_items
FROM sys_order_group og
LEFT JOIN sys_order_item oi ON og.order_id = oi.parent_order_id
WHERE og.create_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY og.order_id, og.status
HAVING group_status != 'CANCELLED' AND cancelled_items > 0
   OR group_status = 'CANCELLED' AND pending_items > 0;
