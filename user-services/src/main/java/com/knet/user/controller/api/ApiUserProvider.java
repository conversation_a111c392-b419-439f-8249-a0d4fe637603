package com.knet.user.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import com.knet.user.model.dto.resp.UserAddressDtoResp;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.service.ISysShopCartService;
import com.knet.user.service.ISysUserAddressService;
import com.knet.user.service.ISysUserOperationRecordService;
import com.knet.user.service.ISysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 16:39
 * @description: 用户服务-对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "用户服务-对外提供接口", description = "用户服务-对外提供接口")
public class ApiUserProvider {
    @Resource
    private ISysUserOperationRecordService sysUserOperationRecordService;
    @Resource
    private ISysUserAddressService sysUserAddressService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private ISysShopCartService shopCartService;

    /**
     * 创建用户操作记录（简化版本，供其他服务调用）
     *
     * @param userId          用户ID
     * @param operatorId      操作者ID
     * @param operatorType    操作者类型
     * @param operationType   操作类型
     * @param operationResult 操作结果
     * @param operationDesc   操作描述
     * @param businessId      关联业务ID
     * @param businessType    关联业务类型
     * @return 操作记录ID
     */
    @Operation(summary = "创建用户操作记录", description = "供其他服务调用的简化版本")
    @PostMapping("/create-simple")
    public HttpResult<String> createSimple(
            @RequestParam("userId") Long userId,
            @RequestParam("operatorId") Long operatorId,
            @RequestParam("operatorType") String operatorType,
            @RequestParam("operationType") UserOperationType operationType,
            @RequestParam("operationResult") OperationResult operationResult,
            @RequestParam("operationDesc") String operationDesc,
            @RequestParam(value = "businessId", required = false) String businessId,
            @RequestParam(value = "businessType", required = false) String businessType) {
        log.info("创建用户操作记录（简化版）: userId={}, operationType={}, operationResult={}", userId, operationType, operationResult);
        String operationId = sysUserOperationRecordService.createOperationRecord(
                userId, operatorId, operatorType, operationType, operationResult,
                operationDesc, businessId, businessType);
        return HttpResult.ok(operationId);
    }

    /**
     * 根据地址ID获取地址信息
     *
     * @param addressId 地址ID
     * @return 地址信息
     */
    @GetMapping("/address/{addressId}")
    @Operation(summary = "根据地址ID获取地址信息", description = "供其他服务调用，获取用户地址详细信息")
    public HttpResult<UserAddressDtoResp> getAddressById(
            @Parameter(description = "地址ID", required = true, example = "1")
            @PathVariable("addressId") Long addressId) {
        log.info("获取地址信息: addressId={}", addressId);
        try {
            UserAddressDtoResp address = sysUserAddressService.getById(addressId).mapToUserInfoDtoResp();
            if (address == null) {
                log.warn("地址不存在: addressId={}", addressId);
                return HttpResult.error("地址不存在");
            }
            log.info("获取地址信息成功: addressId={}", addressId);
            return HttpResult.ok(address);
        } catch (Exception e) {
            log.error("获取地址信息失败: addressId={}, error={}", addressId, e.getMessage(), e);
            return HttpResult.error("获取地址信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取用户信息", description = "供其他服务调用，获取用户详细信息")
    public HttpResult<UserInfoDtoResp> getUserById(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable("userId") Long userId) {
        log.info("获取用户信息: userId={}", userId);
        try {
            UserInfoDtoResp user = sysUserService.getUserById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return HttpResult.error("用户不存在");
            }
            log.info("获取用户信息成功: userId={}", userId);
            return HttpResult.ok(user);
        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}, error={}", userId, e.getMessage(), e);
            return HttpResult.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    @PostMapping("/users/batch")
    @Operation(summary = "批量获取用户信息", description = "供其他服务调用，批量获取用户详细信息")
    public HttpResult<List<UserInfoDtoResp>> getUsersByIds(
            @Parameter(description = "用户ID列表", required = true)
            @RequestBody List<Long> userIds) {
        log.info("批量获取用户信息: userIds={}", userIds);
        try {
            List<UserInfoDtoResp> users = sysUserService.getUsersByIds(userIds);
            log.info("批量获取用户信息成功: 查询{}个用户，返回{}个用户", userIds.size(), users.size());
            return HttpResult.ok(users);
        } catch (Exception e) {
            log.error("批量获取用户信息失败: userIds={}, error={}", userIds, e.getMessage(), e);
            return HttpResult.error("批量获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 清空用户购物车
     *
     * @param userId 用户ID
     * @return void
     */
    @Operation(summary = "清空用户购物车", description = "清空购物车")
    @GetMapping("/shop-cart/remove/{userId}")
    public HttpResult<Void> removeFromCart(@Parameter(description = "用户ID", required = true, example = "1")
                                           @PathVariable("userId") Long userId) {
        shopCartService.deleteCart(userId);
        return HttpResult.ok();
    }

    /**
     * 根据账户名模糊搜索获取用户ID列表
     *
     * @param account 账户名（模糊搜索）
     * @return 用户ID列表
     */
    @PostMapping("/users/userIds-by-account")
    @Operation(summary = "根据账户名模糊搜索获取用户ID列表", description = "供其他服务调用，根据账户名模糊搜索获取用户ID列表")
    public HttpResult<List<Long>> getUserIdsByAccount(
            @Parameter(description = "账户名（模糊搜索）", required = true)
            @RequestParam("account") String account) {
        log.info("根据账户名模糊搜索获取用户ID列表: account={}", account);
        try {
            List<Long> userIds = sysUserService.getUserIdsByAccount(account);
            log.info("根据账户名模糊搜索获取用户ID列表成功: account={}, 返回{}个用户ID", account, userIds.size());
            return HttpResult.ok(userIds);
        } catch (Exception e) {
            log.error("根据账户名模糊搜索获取用户ID列表失败: account={}, error={}", account, e.getMessage(), e);
            return HttpResult.error("根据账户名模糊搜索获取用户ID列表失败: " + e.getMessage());
        }
    }
}
