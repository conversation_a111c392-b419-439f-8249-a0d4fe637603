package com.knet.order.controller;

import com.knet.common.base.HttpResult;
import com.knet.common.utils.S3FileUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传下载测试接口
 * 用于测试AWS S3文件上传下载功能
 *
 * <AUTHOR>
 * @date 2025/09/10
 */
@Slf4j
@RestController
@RequestMapping("/api/test/file")
@Tag(name = "文件上传下载测试", description = "用于测试AWS S3文件上传下载功能的接口")
public class FileTestController {

    @Resource
    private S3FileUtil s3FileUtil;

    /**
     * 生成预签名上传URL
     */
    @PostMapping("/upload-url")
    @Operation(summary = "生成预签名上传URL", description = "生成用于直接上传到S3的预签名URL")
    public HttpResult<S3FileUtil.PresignedUploadInfo> generateUploadUrl(
            @Parameter(description = "文件名", required = true) @RequestParam String fileName,
            @Parameter(description = "文件类型 (MIME type)", required = true) @RequestParam String fileType,
            @Parameter(description = "文件夹路径 (可选)") @RequestParam(required = false) String folder) {

        try {
            log.info("生成预签名上传URL请求: fileName={}, fileType={}, folder={}", fileName, fileType, folder);

            S3FileUtil.PresignedUploadInfo uploadInfo = s3FileUtil.generatePresignedUploadUrl(fileName, fileType, folder);

            log.info("生成预签名上传URL成功: {}", uploadInfo.getFileKey());
            return HttpResult.ok(uploadInfo);

        } catch (Exception e) {
            log.error("生成预签名上传URL失败", e);
            return HttpResult.error("生成预签名上传URL失败: " + e.getMessage());
        }
    }

    /**
     * 生成预签名下载URL
     */
    @GetMapping("/download-url")
    @Operation(summary = "生成预签名下载URL", description = "根据文件key生成预签名下载URL")
    public HttpResult<String> generateDownloadUrl(
            @Parameter(description = "文件在S3中的key", required = true) @RequestParam String fileKey) {

        try {
            log.info("生成预签名下载URL请求: fileKey={}", fileKey);

            String downloadUrl = s3FileUtil.generatePresignedDownloadUrl(fileKey);

            log.info("生成预签名下载URL成功: {}", downloadUrl);
            return HttpResult.ok(downloadUrl);

        } catch (Exception e) {
            log.error("生成预签名下载URL失败", e);
            return HttpResult.error("生成预签名下载URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取文件信息", description = "根据文件key获取文件详细信息")
    public HttpResult<S3FileUtil.FileInfo> getFileInfo(
            @Parameter(description = "文件在S3中的key", required = true) @RequestParam String fileKey) {

        try {
            log.info("获取文件信息请求: fileKey={}", fileKey);

            S3FileUtil.FileInfo fileInfo = s3FileUtil.getFileInfo(fileKey);

            log.info("获取文件信息成功: {}", fileInfo.getFileKey());
            return HttpResult.ok(fileInfo);

        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return HttpResult.error("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/exists")
    @Operation(summary = "检查文件是否存在", description = "检查指定文件key是否存在于S3中")
    public HttpResult<Map<String, Object>> checkFileExists(
            @Parameter(description = "文件在S3中的key", required = true) @RequestParam String fileKey) {

        try {
            log.info("检查文件是否存在请求: fileKey={}", fileKey);

            boolean exists = s3FileUtil.fileExists(fileKey);

            Map<String, Object> result = new HashMap<>();
            result.put("fileKey", fileKey);
            result.put("exists", exists);

            log.info("检查文件是否存在完成: fileKey={}, exists={}", fileKey, exists);
            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("检查文件是否存在失败", e);
            return HttpResult.error("检查文件是否存在失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "从S3中删除指定的文件")
    public HttpResult<Map<String, Object>> deleteFile(
            @Parameter(description = "文件在S3中的key", required = true) @RequestParam String fileKey) {

        try {
            log.info("删除文件请求: fileKey={}", fileKey);

            boolean success = s3FileUtil.deleteFile(fileKey);

            Map<String, Object> result = new HashMap<>();
            result.put("fileKey", fileKey);
            result.put("deleted", success);

            log.info("删除文件完成: fileKey={}, success={}", fileKey, success);
            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("删除文件失败", e);
            return HttpResult.error("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取上传使用说明
     */
    @GetMapping("/upload-guide")
    @Operation(summary = "获取上传使用说明", description = "获取如何使用预签名URL上传文件的详细说明")
    public HttpResult<Map<String, Object>> getUploadGuide() {
        Map<String, Object> guide = new HashMap<>();

        guide.put("title", "AWS S3 预签名URL上传使用说明");
        guide.put("description", "通过预签名URL直接上传文件到S3的步骤说明");

        Map<String, Object> steps = new HashMap<>();
        steps.put("step1", "调用 /api/test/file/upload-url 接口获取预签名上传URL");
        steps.put("step2", "使用返回的uploadUrl进行PUT请求上传文件");
        steps.put("step3", "上传成功后可通过downloadUrl访问文件");
        guide.put("steps", steps);

        Map<String, Object> curlExample = new HashMap<>();
        curlExample.put("description", "使用curl上传文件的示例");
        curlExample.put("command", "curl -X PUT \"<uploadUrl>\" -H \"Content-Type: <fileType>\" --data-binary @/path/to/your/file.jpg");
        guide.put("curlExample", curlExample);

        Map<String, Object> jsExample = new HashMap<>();
        jsExample.put("description", "使用JavaScript上传文件的示例");
        jsExample.put("code", """
                const file = document.getElementById('fileInput').files[0];
                fetch(uploadUrl, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': file.type
                  },
                  body: file
                }).then(response => {
                  if (response.ok) {
                    console.log('上传成功');
                  }
                });""");
        guide.put("jsExample", jsExample);

        return HttpResult.ok(guide);
    }

    /**
     * 上传文件 (Base64编码)
     */
    @PostMapping("/upload-base64")
    @Operation(summary = "上传文件(Base64)", description = "通过Base64编码直接上传文件到S3")
    public HttpResult<Map<String, Object>> uploadFileByBase64(
            @Parameter(description = "Base64编码的文件内容", required = true) @RequestParam String fileContent,
            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {

        try {
            log.info("上传文件请求(Base64): fileName={}, contentLength={}", fileName, fileContent.length());

            // 解码Base64内容
            byte[] fileBytes = java.util.Base64.getDecoder().decode(fileContent);

            String downloadUrl = s3FileUtil.uploadFile(fileBytes, fileName);

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("downloadUrl", downloadUrl);
            result.put("fileSize", fileBytes.length);
            result.put("uploadTime", java.time.LocalDateTime.now());

            log.info("文件上传成功(Base64): fileName={}, downloadUrl={}, size={} bytes", fileName, downloadUrl, fileBytes.length);
            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("文件上传失败(Base64)", e);
            return HttpResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件 (MultipartFile)
     */
    @PostMapping("/upload-multipart")
    @Operation(summary = "上传文件(MultipartFile)", description = "通过表单文件上传到S3")
    public HttpResult<Map<String, Object>> uploadFileByMultipart(
            @Parameter(description = "上传的文件", required = true) @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {

        try {
            if (file.isEmpty()) {
                return HttpResult.error("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            log.info("上传文件请求(MultipartFile): fileName={}, size={} bytes", fileName, file.getSize());

            // 使用InputStream方式上传
            String downloadUrl = s3FileUtil.uploadFile(file.getInputStream(), fileName);

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("downloadUrl", downloadUrl);
            result.put("fileSize", file.getSize());
            result.put("contentType", file.getContentType());
            result.put("uploadTime", java.time.LocalDateTime.now());

            log.info("文件上传成功(MultipartFile): fileName={}, downloadUrl={}, size={} bytes", fileName, downloadUrl, file.getSize());
            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("文件上传失败(MultipartFile)", e);
            return HttpResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文本文件
     */
    @PostMapping("/upload-text")
    @Operation(summary = "上传文本文件", description = "将文本内容直接上传为文件到S3")
    public HttpResult<Map<String, Object>> uploadTextFile(
            @Parameter(description = "文本内容", required = true) @RequestParam String content,
            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {

        try {
            log.info("上传文本文件请求: fileName={}, contentLength={}", fileName, content.length());

            // 将文本转换为字节数组
            byte[] fileBytes = content.getBytes(java.nio.charset.StandardCharsets.UTF_8);

            String downloadUrl = s3FileUtil.uploadFile(fileBytes, fileName);

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("downloadUrl", downloadUrl);
            result.put("fileSize", fileBytes.length);
            result.put("contentPreview", content.length() > 100 ? content.substring(0, 100) + "..." : content);
            result.put("uploadTime", java.time.LocalDateTime.now());

            log.info("文本文件上传成功: fileName={}, downloadUrl={}, size={} bytes", fileName, downloadUrl, fileBytes.length);
            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("文本文件上传失败", e);
            return HttpResult.error("文本文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传测试
     */
    @PostMapping("/upload-batch-test")
    @Operation(summary = "批量上传测试", description = "创建多个测试文件并批量上传到S3")
    public HttpResult<Map<String, Object>> batchUploadTest(
            @Parameter(description = "文件数量", required = false) @RequestParam(defaultValue = "3") int fileCount) {

        try {
            log.info("批量上传测试开始: fileCount={}", fileCount);

            java.util.List<Map<String, Object>> uploadResults = new java.util.ArrayList<>();

            for (int i = 1; i <= fileCount; i++) {
                // 创建测试文件内容
                String content = String.format("这是测试文件 #%d\n创建时间: %s\n文件编号: %d",
                        i, java.time.LocalDateTime.now(), i);
                String fileName = String.format("test-file-%d.txt", i);

                try {
                    byte[] fileBytes = content.getBytes(java.nio.charset.StandardCharsets.UTF_8);
                    String downloadUrl = s3FileUtil.uploadFile(fileBytes, fileName);

                    Map<String, Object> fileResult = new HashMap<>();
                    fileResult.put("fileName", fileName);
                    fileResult.put("downloadUrl", downloadUrl);
                    fileResult.put("fileSize", fileBytes.length);
                    fileResult.put("status", "success");

                    uploadResults.add(fileResult);
                    log.info("批量上传 - 文件{}上传成功: {}", i, fileName);

                } catch (Exception e) {
                    Map<String, Object> fileResult = new HashMap<>();
                    fileResult.put("fileName", fileName);
                    fileResult.put("status", "failed");
                    fileResult.put("error", e.getMessage());

                    uploadResults.add(fileResult);
                    log.error("批量上传 - 文件{}上传失败: {}", i, fileName, e);
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalFiles", fileCount);
            result.put("successCount", uploadResults.stream().mapToInt(r -> "success".equals(r.get("status")) ? 1 : 0).sum());
            result.put("failedCount", uploadResults.stream().mapToInt(r -> "failed".equals(r.get("status")) ? 1 : 0).sum());
            result.put("uploadResults", uploadResults);
            result.put("uploadTime", java.time.LocalDateTime.now());

            log.info("批量上传测试完成: 总数={}, 成功={}, 失败={}",
                    result.get("totalFiles"), result.get("successCount"), result.get("failedCount"));

            return HttpResult.ok(result);

        } catch (Exception e) {
            log.error("批量上传测试失败", e);
            return HttpResult.error("批量上传测试失败: " + e.getMessage());
        }
    }
}
