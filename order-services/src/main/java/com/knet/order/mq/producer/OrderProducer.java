package com.knet.order.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.order.openfeign.ApiDelayedProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static com.knet.common.constants.OrderServicesConstants.*;

/**
 * <AUTHOR>
 * @date 2025/3/19 13:31
 * @description: 订单生产者
 */
@Slf4j
@Component
public class OrderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private ApiDelayedProvider apiDelayedProvider;

    /**
     * 发送订单创建成功事件
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderCreateEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", ORDER_CREATED);
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.created",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("order.created 消息到达Broker: {}", messageId);
                    } else {
                        log.error("order.created 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("order.created 消息发送异常: {}", ex.getMessage());
                }
        );
    }

    /**
     * 发送订单创建失败事件
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderCreateFailedEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "order.failed");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.failed",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("order.failed 消息到达Broker: {}", messageId);
                    } else {
                        log.error("order.failed 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("order.failed 消息发送异常: {}", ex.getMessage());
                }
        );
    }

    /**
     * 发送延迟信息队列，用于创建订单后5分钟未支付，自动取消订单
     *
     * @param messageBody me
     * @return 延迟消息ID
     */
    public String sendDelayedMessage(String messageBody) {
        long triggerTime = System.currentTimeMillis() + ORDER_TIMEOUT_TIME;
        log.info("发送延迟消息: {},触发时间={}", messageBody, triggerTime);
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        DelayedMessage delayedMessage = DelayedMessage.builder()
                .id(messageId)
                .payloadJson(messageBody)
                .triggerTime(triggerTime)
                .targetExchange("order.delayed.exchange")
                .targetRoutingKey("timeout.order")
                .build();
        apiDelayedProvider.addDelayedMessage(delayedMessage);
        return messageId;
    }

    /**
     * 取消延迟消息
     *
     * @param orderId 订单ID
     */
    public void cancelDelayedMessage(String orderId) {
        try {
            log.info("取消延迟消息: orderId={}", orderId);
            HttpResult<String> result = apiDelayedProvider.cancelDelayedMessage(orderId);
            if (result != null && result.success()) {
                log.info("延迟消息取消成功: orderId={}, result={}", orderId, result.getMsg());
            } else {
                log.warn("延迟消息取消失败: orderId={}, result={}", orderId, result != null ? result.getMsg() : "null");
            }
        } catch (Exception e) {
            log.error("取消延迟消息异常: orderId={}, error={}", orderId, e.getMessage(), e);
        }
    }

    /**
     * 发送订单退款消息
     *
     * @param messageBody 消息体
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderRefundEvent(String messageBody) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "order.refund");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "order-exchange",
                "order.refund",
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("order.refund 消息到达Broker: {}", messageId);
                    } else {
                        log.error("order.refund 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("order.refund 消息发送异常: {}", ex.getMessage());
                }
        );
    }

    /**
     * 发送订单通知消息到notification-service
     *
     * @param notificationMessage 通知消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendOrderNotificationMessage(NotificationMessage notificationMessage) {
        String messageId = String.format(KNET_B2B_ORDER_MESSAGE_PREFIX, RandomUtil.randomString(16));
        String messageBody = JSON.toJSONString(notificationMessage);
        log.info("发送订单通知消息: messageId={}, content={}", messageId, notificationMessage.getContent());

        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "notification.order");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);

        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "notification-exchange",
                "notification.order",
                message,
                correlationData
        );

        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("订单通知消息到达Broker: {}", messageId);
                    } else {
                        log.error("订单通知消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("订单通知消息发送异常: {}", ex.getMessage());
                }
        );
    }
}
