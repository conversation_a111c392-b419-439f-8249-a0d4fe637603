package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.dto.message.OrderRefundMessage;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.goods.model.dto.third.resp.KnetSubOrderGroupDto;
import com.knet.goods.model.dto.third.resp.KnetSysOrderItem;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.openfeign.ApiOrderServiceProvider;
import com.knet.goods.service.IInventoryCompensationService;
import com.knet.goods.service.IKnetProductService;
import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/19 10:25
 * @description: 库存补偿服务实现
 */
@Slf4j
@Service
public class InventoryCompensationServiceImpl implements IInventoryCompensationService {

    @Resource
    private ApiOrderServiceProvider apiOrderServiceProvider;
    @Resource
    private IKnetProductService knetProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderTimeout(String messageBody) {
        log.info("商品服务处理订单超时补偿: {}", messageBody);
        OrderMessage message = JSON.parseObject(messageBody, OrderMessage.class);
        try {
            String orderId = message.getOrderId();

            // 方案2：延迟消息去重机制，防止重复处理同一订单的超时事件
            String timeoutKey = "order:timeout:processed:" + orderId;
            if (RedisCacheUtil.hasKey(timeoutKey)) {
                log.info("订单超时已处理过，跳过重复处理: orderId={}", orderId);
                return;
            }

            // 首先验证订单状态，避免重复处理已取消的订单
            if (!isOrderEligibleForTimeout(orderId)) {
                log.info("订单不符合超时处理条件，跳过库存释放: orderId={}", orderId);
                return;
            }

            // 标记订单超时处理开始，防止重复处理（设置1小时过期）
            RedisCacheUtil.set(timeoutKey, "processing", 3600);

            HttpResult<KnetSubOrderGroupDto> dbOrderGroup = apiOrderServiceProvider.getOrderGroupByOrderId(orderId);
            if (dbOrderGroup == null || !dbOrderGroup.success() || dbOrderGroup.getData() == null) {
                log.error("获取订单信息失败或订单不存在: orderId={}, result={}",
                        orderId, dbOrderGroup != null ? dbOrderGroup.getMsg() : "null");
                return;
            }
            KnetSubOrderGroupDto orderGroup = dbOrderGroup.getData();
            if (KnetOrderGroupStatus.isReleasableState(orderGroup.getStatus())) {
                log.info("订单状态={}，无需释放库存: orderId={}", orderGroup.getStatus(), message.getOrderId());
                return;
            }
            String orderId = message.getOrderId();
            Long userId = message.getUserId();
            //如果订单处于 MULTIPLE_STATES状态需要释放指定的商品库存
            if (KnetOrderGroupStatus.MULTIPLE_STATES.equals(orderGroup.getStatus())) {
                HttpResult<List<KnetSysOrderItem>> orderItemDetail = apiOrderServiceProvider.getOrderItemList(orderId);
                if (orderItemDetail == null || !orderItemDetail.success() || orderItemDetail.getData() == null || orderItemDetail.getData().isEmpty()) {
                    log.info("订单对应的商品明细获取失败或不存在，无需释放库存: orderId={}, result={}",
                            orderId, orderItemDetail != null ? orderItemDetail.getMsg() : "null");
                    return;
                }
                log.info("开始释放订单库存: orderId={}, 商品项数量={}", orderId, orderItemDetail.getData().size());
                List<KnetSysOrderItem> itemDetailData = orderItemDetail.getData();
                List<String> releaseList = itemDetailData.stream()
                        .filter(item -> KnetOrderItemStatus.CANCELLED.equals(item.getStatus()))
                        .map(KnetSysOrderItem::getOneId)
                        .filter(StrUtil::isNotBlank)
                        .distinct()
                        .toList();
                if (releaseList.isEmpty()) {
                    log.info("没有需要释放的库存: orderId={}", orderId);
                    return;
                }
                // 使用原子性操作释放库存，逐个处理确保状态一致性
                int actualReleased = 0;
                for (String oneId : releaseList) {
                    try {
                        // 查询当前商品状态
                        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper
                                .eq(KnetProduct::getOneId, oneId)
                                .eq(KnetProduct::getStatus, ProductStatus.LOCKED);
                        KnetProduct lockedProduct = knetProductService.getOne(queryWrapper);
                        if (lockedProduct == null) {
                            log.warn("未找到需要释放的锁定商品: oneId={}", oneId);
                            continue;
                        }
                        // 使用乐观锁释放库存
                        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper
                                .eq(KnetProduct::getId, lockedProduct.getId())
                                .eq(KnetProduct::getStatus, ProductStatus.LOCKED)
                                .eq(KnetProduct::getVersion, lockedProduct.getVersion())
                                .set(KnetProduct::getStatus, ProductStatus.ON_SALE)
                                .set(KnetProduct::getVersion, lockedProduct.getVersion() + 1);
                        boolean updated = knetProductService.update(updateWrapper);
                        if (updated) {
                            actualReleased++;
                            log.debug("库存释放成功: oneId={}, productId={}, version={}->{}",
                                    oneId, lockedProduct.getId(), lockedProduct.getVersion(), lockedProduct.getVersion() + 1);
                        } else {
                            log.warn("库存释放失败，可能被其他线程修改: oneId={}, productId={}, version={}",
                                    oneId, lockedProduct.getId(), lockedProduct.getVersion());
                        }
                    } catch (Exception e) {
                        log.error("释放单个商品库存失败: oneId={}, error={}", oneId, e.getMessage(), e);
                    }
                }
                log.info("库存释放完成: orderId={}, 需要释放的oneId数量={}, 实际释放数量={}, oneIds={}",
                        orderId, releaseList.size(), actualReleased, releaseList);
            } else {
                // 1. 通过订单服务获取订单项信息
                HttpResult<List<SubOrderItemResp>> orderItemsResult = apiOrderServiceProvider.getOrderItemsByOrderId(orderId);
                if (orderItemsResult == null || !orderItemsResult.success() || orderItemsResult.getData() == null || orderItemsResult.getData().isEmpty()) {
                    log.info("订单对应的商品明细获取失败或不存在，无需释放库存: orderId={}, result={}",
                            orderId, orderItemsResult != null ? orderItemsResult.getMsg() : "null");
                    return;
                }
                List<SubOrderItemResp> orderItems = orderItemsResult.getData();
                log.info("开始释放订单库存: orderId={}, 商品项数量={}", orderId, orderItems.size());
                // 2. 遍历订单项，释放对应的库存
                for (SubOrderItemResp item : orderItems) {
                    releaseInventoryForItem(item, orderId);
                }
                log.info("订单超时库存释放完成: orderId={}, userId={}", orderId, userId);
            }
        } catch (Exception e) {
            log.error("处理订单超时库存释放异常: orderId={}, error={}", message.getOrderId(), e.getMessage(), e);
            throw new ServiceException("处理订单超时库存释放异常: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processOrderRefund(String messageBody) {
        log.info("商品服务处理订单退款补偿: {}", messageBody);
        OrderRefundMessage orderMessage = JSON.parseObject(messageBody, OrderRefundMessage.class);
        HttpResult<KnetSysOrderItem> orderItemDetail = apiOrderServiceProvider.getOrderItemDetail(String.valueOf(orderMessage.getOrderItemId()));
        if (orderItemDetail == null || !orderItemDetail.success() || orderItemDetail.getData() == null || BeanUtil.isEmpty(orderItemDetail.getData())) {
            log.info("订单服务获取订单项信息失败或不存在，无需释放库存: orderItemId={}, result={}",
                    orderMessage.getOrderItemId(), orderItemDetail != null ? orderItemDetail.getMsg() : "null");
            return;
        }
        ProductStatus targetStatus = ProductStatus.ON_SALE;
        String operation = "释放库存";
        // 检查是否需要释放库存，如果releaseInventory为false或null，则不释放库存，商品下架
        if (orderMessage.getReleaseInventory() == null || !orderMessage.getReleaseInventory()) {
            targetStatus = ProductStatus.OFF_SALE;
            operation = "下架";
            log.info("API取消订单，不释放库存，产品下架: orderItemId={}", orderMessage.getOrderItemId());
        }
        try {
            String oneId = orderItemDetail.getData().getOneId();
            if (oneId == null || oneId.isEmpty()) {
                log.warn("订单项oneId为空，无法{}库存: orderItemId={}, prentOrderId={}",
                        operation, orderMessage.getOrderItemId(), orderMessage.getPrentOrderId());
                return;
            }
            LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(KnetProduct::getOneId, oneId)
                    .eq(KnetProduct::getStatus, ProductStatus.LOCKED)
                    .set(KnetProduct::getStatus, targetStatus);
            boolean updated = knetProductService.update(updateWrapper);
            String logBase = String.format("orderItemId=%s, prentOrderId=%s, oneId=%s",
                    orderMessage.getOrderItemId(),
                    orderMessage.getPrentOrderId(),
                    oneId);
            if (updated) {
                log.info("库存{}成功: {}", operation, logBase);
            } else {
                log.warn("未找到需要{}的锁定库存: {}", operation, logBase);
            }
        } catch (Exception e) {
            log.error("库存{}失败: {}, 原因: {}", operation,
                    String.format("orderItemId=%s, prentOrderId=%s, oneId=%s",
                            orderMessage.getOrderItemId(),
                            orderMessage.getPrentOrderId(),
                            orderItemDetail.getData().getOneId()),
                    e.getMessage());
        }
    }

    /**
     * 释放单个商品项的库存
     * 修复问题：只释放确实属于当前订单的库存，避免释放其他订单的库存
     *
     * @param item    订单商品项
     * @param orderId 订单ID
     */
    private void releaseInventoryForItem(SubOrderItemResp item, String orderId) {
        try {
            String sku = item.getSku();
            String size = item.getSize();
            String priceStr = item.getPrice();
            Integer count = item.getCount();

            // 将策略价格转换为原始价格进行库存释放
            Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(priceStr);
            Long originalPriceCents = PriceFormatUtil.formatYuanToCents(priceStr);
            log.info("释放商品库存价格转换: orderId={}, sku={}, 策略价格={}美分, 原始价格={}美分",
                    orderId, sku, strategyPriceCents, originalPriceCents);
            log.info("释放商品库存: orderId={}, sku={}, size={}, originalPrice={}, count={}",
                    orderId, sku, size, originalPriceCents, count);

            // 方案1：通过订单服务获取该订单项对应的oneId列表，只释放这些特定的库存
            HttpResult<List<KnetSysOrderItem>> orderItemDetail = apiOrderServiceProvider.getOrderItemDetailByOrderId(orderId);
            if (orderItemDetail == null || !orderItemDetail.success() || orderItemDetail.getData() == null) {
                log.warn("获取订单项详情失败，无法精确释放库存: orderId={}", orderId);
                return;
            }

            // 筛选出匹配当前商品项的订单项，获取其oneId列表
            List<String> oneIdsToRelease = orderItemDetail.getData().stream()
                    .filter(orderItem -> sku.equals(orderItem.getSku())
                            && size.equals(orderItem.getSize())
                            && priceStr.equals(orderItem.getPrice().toString())
                            && StrUtil.isNotBlank(orderItem.getOneId()))
                    .map(KnetSysOrderItem::getOneId)
                    .distinct()
                    .toList();

            if (oneIdsToRelease.isEmpty()) {
                log.warn("未找到需要释放的oneId列表: orderId={}, sku={}, size={}", orderId, sku, size);
                return;
            }

            log.info("开始精确释放库存: orderId={}, sku={}, size={}, oneIds={}",
                    orderId, sku, size, oneIdsToRelease);

            int actualReleased = 0;
            // 逐个释放指定的oneId库存
            for (String oneId : oneIdsToRelease) {
                try {
                    // 查询指定oneId的锁定商品
                    LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(KnetProduct::getOneId, oneId)
                            .eq(KnetProduct::getStatus, ProductStatus.LOCKED);
                    KnetProduct lockedProduct = knetProductService.getOne(queryWrapper);

                    if (lockedProduct == null) {
                        log.warn("未找到锁定状态的商品: oneId={}, orderId={}", oneId, orderId);
                        continue;
                    }

                    // 使用乐观锁释放库存
                    LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper
                            .eq(KnetProduct::getId, lockedProduct.getId())
                            .eq(KnetProduct::getStatus, ProductStatus.LOCKED)
                            .eq(KnetProduct::getVersion, lockedProduct.getVersion())
                            .set(KnetProduct::getStatus, ProductStatus.ON_SALE)
                            .set(KnetProduct::getVersion, lockedProduct.getVersion() + 1);
                    boolean updated = knetProductService.update(updateWrapper);
                    if (updated) {
                        actualReleased++;
                        log.debug("库存释放成功: oneId={}, productId={}, version={}->{}",
                                oneId, lockedProduct.getId(), lockedProduct.getVersion(), lockedProduct.getVersion() + 1);
                    } else {
                        log.warn("库存释放失败，可能被其他线程修改: oneId={}, productId={}, version={}",
                                oneId, lockedProduct.getId(), lockedProduct.getVersion());
                    }
                } catch (Exception e) {
                    log.error("释放单个商品库存失败: oneId={}, error={}", oneId, e.getMessage(), e);
                }
            }

            log.info("库存释放完成: orderId={}, sku={}, size={}, 需要释放数量={}, 实际释放数量={}",
                    orderId, sku, size, oneIdsToRelease.size(), actualReleased);

        } catch (Exception e) {
            log.error("释放商品库存失败: orderId={}, sku={}, size={}, error={}",
                    orderId, item.getSku(), item.getSize(), e.getMessage(), e);
            // 不抛出异常，继续处理其他商品项
        }
    }

    /**
     * 验证订单是否符合超时处理条件
     * 防止重复处理已经取消或已支付的订单
     *
     * @param orderId 订单ID
     * @return 是否符合超时处理条件
     */
    private boolean isOrderEligibleForTimeout(String orderId) {
        try {
            HttpResult<KnetSubOrderGroupDto> orderResult = apiOrderServiceProvider.getOrderGroupByOrderId(orderId);
            if (orderResult == null || !orderResult.success() || orderResult.getData() == null) {
                log.warn("无法获取订单状态，跳过超时处理: orderId={}", orderId);
                return false;
            }

            KnetSubOrderGroupDto orderGroup = orderResult.getData();
            String currentStatus = orderGroup.getStatus();

            // 只有待支付状态的订单才需要进行超时处理
            if (!"PENDING_PAYMENT".equals(currentStatus)) {
                log.info("订单状态不是待支付，无需超时处理: orderId={}, currentStatus={}", orderId, currentStatus);
                return false;
            }

            log.info("订单符合超时处理条件: orderId={}, status={}", orderId, currentStatus);
            return true;
        } catch (Exception e) {
            log.error("验证订单超时处理条件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            return false;
        }
    }
}
