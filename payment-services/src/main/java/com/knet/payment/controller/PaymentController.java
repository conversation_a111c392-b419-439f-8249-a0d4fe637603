package com.knet.payment.controller;

import com.knet.common.base.HttpResult;
import com.knet.payment.model.dto.req.PayPaymentRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.model.dto.resp.PayResponse;
import com.knet.payment.mq.producer.PaymentMessageProducer;
import com.knet.payment.service.IPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/6/6 13:59
 * @description: 支付服务-用户支付控制器
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@Tag(name = "支付服务-用户支付控制器", description = "支付服务-用户支付控制器")
public class PaymentController {
    @Resource
    private IPaymentService paymentService;
    @Resource
    private PaymentMessageProducer paymentMessageProducer;

    /**
     * 用户支付
     *
     * @param request 用户支付请求
     * @return 用户支付响应
     */
    @PostMapping("/pay")
    @Operation(summary = "用户支付", description = "用户支付")
    public HttpResult<PayResponse> payPayment(@Valid @RequestBody PayPaymentRequest request) {
        log.info("用户支付请求: userId={}, orderId={}, amount={}, channel={}",
                request.getUserId(), request.getOrderId(), request.getAmount(), request.getPaymentChannel());
        PayResponse response = paymentService.payPayment(request);
/*        if (StrUtil.isNotBlank(request.getEmail())) {
            NotificationMessage notificationMessage = NotificationMessage.createPaymentNotice(request.getEmail(), request.getOrderId());
            paymentMessageProducer.sendNotificationMessage(notificationMessage);
        }*/
        log.info("用户支付成功: groupId={}, status={}", response.getGroupId(), response.getStatus());
        return HttpResult.ok(response);
    }

    /**
     * 查询支付状态
     *
     * @param paymentId 支付流水ID
     * @return 支付状态信息
     */
    @GetMapping("/status/{paymentId}")
    @Operation(summary = "查询支付状态", description = "根据支付流水ID查询支付状态")
    public HttpResult<CreatePaymentResponse> queryPaymentStatus(
            @Parameter(description = "支付流水ID", required = true, example = "GRP-123456789012345678")
            @PathVariable String paymentId) {
        log.info("查询支付状态: paymentId={}", paymentId);
        try {
            CreatePaymentResponse response = paymentService.queryPaymentStatus(paymentId);
            return HttpResult.ok(response);
        } catch (Exception e) {
            log.error("查询支付状态失败: paymentId={}, error={}", paymentId, e.getMessage(), e);
            return HttpResult.error(e.getMessage());
        }
    }
}
