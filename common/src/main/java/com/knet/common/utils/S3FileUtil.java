package com.knet.common.utils;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * AWS S3 文件上传下载工具类
 * 基于AWS SDK v2实现，手动生成预签名URL
 *
 * <AUTHOR>
 * @date 2025/09/10
 */
@Slf4j
@Component
public class S3FileUtil {

    private final String bucketName = "knetb2b";

    private final String region = "us-west-2";

    private final Long presignedUrlDuration = 3600L;

    private String cdnDomain = "https://uploadsb2b.knetgroup.com";

    private S3Client s3Client;

    @PostConstruct
    public void init() {
        try {
            // 使用默认凭证提供者，支持EC2实例角色
            this.s3Client = S3Client.builder()
                    .region(Region.of(region))
                    .credentialsProvider(DefaultCredentialsProvider.builder().build())
                    .build();
            log.info("S3FileUtil 初始化成功, bucket: {}, region: {}", bucketName, region);
        } catch (Exception e) {
            log.error("S3FileUtil 初始化失败", e);
            throw new RuntimeException("S3FileUtil 初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (s3Client != null) {
            s3Client.close();
        }
        log.info("S3FileUtil 资源释放完成");
    }

    /**
     * 生成预签名上传URL
     *
     * @param fileName 文件名
     * @param fileType 文件类型 (MIME type)
     * @param folder   文件夹路径 (可选)
     * @return 预签名上传URL信息
     */
    public PresignedUploadInfo generatePresignedUploadUrl(String fileName, String fileType, String folder) {
        try {
            String key = generateFileKey(fileName, folder);
            // 手动构建预签名PUT URL
            String uploadUrl = generatePresignedPutUrl(key, fileType);
            String downloadUrl = generateDownloadUrl(key);
            log.info("生成预签名上传URL成功, key: {}, uploadUrl: {}", key, uploadUrl);
            return PresignedUploadInfo.builder()
                    .uploadUrl(uploadUrl)
                    .downloadUrl(downloadUrl)
                    .fileKey(key)
                    .fileName(fileName)
                    .expiresIn(presignedUrlDuration)
                    .fields(generateUploadFields(key, fileType))
                    .build();
        } catch (Exception e) {
            log.error("生成预签名上传URL失败, fileName: {}, fileType: {}", fileName, fileType, e);
            throw new RuntimeException("生成预签名上传URL失败", e);
        }
    }

    /**
     * 生成预签名下载URL
     *
     * @param fileKey 文件在S3中的key
     * @return 预签名下载URL
     */
    public String generatePresignedDownloadUrl(String fileKey) {
        try {
            // 手动构建预签名GET URL
            String downloadUrl = generatePresignedGetUrl(fileKey);
            log.info("生成预签名下载URL成功, key: {}, downloadUrl: {}", fileKey, downloadUrl);
            return downloadUrl;
        } catch (Exception e) {
            log.error("生成预签名下载URL失败, fileKey: {}", fileKey, e);
            throw new RuntimeException("生成预签名下载URL失败", e);
        }
    }

    /**
     * 上传文件 (字节数组)
     *
     * @param fileBytes 文件字节数组
     * @param fileName  文件名
     * @return 文件下载地址
     */
    public String uploadFile(byte[] fileBytes, String fileName) {
        try {
            String key = generateFileKey(fileName, null);
            String contentType = getContentTypeFromFileName(fileName);
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength((long) fileBytes.length)
                    .build();
            s3Client.putObject(putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
            String downloadUrl = generateDownloadUrl(key);
            log.info("文件上传成功, key: {}, fileName: {}, size: {} bytes", key, fileName, fileBytes.length);
            return downloadUrl;
        } catch (Exception e) {
            log.error("文件上传失败, fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        }
    }

    /**
     * 上传文件 (输入流)
     *
     * @param inputStream 文件输入流
     * @param fileName    文件名
     * @return 文件下载地址
     */
    public String uploadFile(java.io.InputStream inputStream, String fileName) {
        try {
            String key = generateFileKey(fileName, null);
            String contentType = getContentTypeFromFileName(fileName);
            // 读取输入流长度（如果可能的话）
            java.io.ByteArrayOutputStream buffer = new java.io.ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] fileBytes = buffer.toByteArray();
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength((long) fileBytes.length)
                    .build();
            s3Client.putObject(putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
            String downloadUrl = generateDownloadUrl(key);
            log.info("文件上传成功, key: {}, fileName: {}, size: {} bytes", key, fileName, fileBytes.length);
            return downloadUrl;
        } catch (Exception e) {
            log.error("文件上传失败, fileName: {}", fileName, e);
            throw new RuntimeException("文件上传失败", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (java.io.IOException e) {
                log.warn("关闭输入流失败", e);
            }
        }
    }

    /**
     * 删除文件
     *
     * @param fileKey 文件在S3中的key
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileKey) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.deleteObject(deleteObjectRequest);
            log.info("删除文件成功, key: {}", fileKey);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败, fileKey: {}", fileKey, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param fileKey 文件在S3中的key
     * @return 文件是否存在
     */
    public boolean fileExists(String fileKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.headObject(headObjectRequest);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        } catch (Exception e) {
            log.error("检查文件是否存在失败, fileKey: {}", fileKey, e);
            return false;
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileKey 文件在S3中的key
     * @return 文件信息
     */
    public FileInfo getFileInfo(String fileKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            HeadObjectResponse response = s3Client.headObject(headObjectRequest);
            return FileInfo.builder()
                    .fileKey(fileKey)
                    .contentType(response.contentType())
                    .contentLength(response.contentLength())
                    .lastModified(response.lastModified())
                    .etag(response.eTag())
                    .downloadUrl(generateDownloadUrl(fileKey))
                    .build();
        } catch (Exception e) {
            log.error("获取文件信息失败, fileKey: {}", fileKey, e);
            throw new RuntimeException("获取文件信息失败", e);
        }
    }

    /**
     * 手动生成预签名PUT URL
     */
    private String generatePresignedPutUrl(String key, String contentType) {
        try {
            Instant expiration = Instant.now().plus(Duration.ofSeconds(presignedUrlDuration));
            // 构建预签名URL
            String host = String.format("%s.s3.%s.amazonaws.com", bucketName, region);
            String method = "PUT";
            String canonicalUri = "/" + encodeKey(key);
            // 生成签名参数
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("X-Amz-Algorithm", "AWS4-HMAC-SHA256");
            queryParams.put("X-Amz-Date", formatTimestamp(Instant.now()));
            queryParams.put("X-Amz-SignedHeaders", "host");
            queryParams.put("X-Amz-Expires", String.valueOf(presignedUrlDuration));
            queryParams.put("X-Amz-Credential", generateCredential());
            // 注意：这是一个简化实现，实际生产环境建议使用官方presigner
            String baseUrl = String.format("https://%s%s", host, canonicalUri);
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }
            return baseUrl + "?" + queryString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成预签名PUT URL失败", e);
        }
    }

    /**
     * 手动生成预签名GET URL
     */
    private String generatePresignedGetUrl(String key) {
        try {
            // 构建预签名URL
            String host = String.format("%s.s3.%s.amazonaws.com", bucketName, region);
            String canonicalUri = "/" + encodeKey(key);
            // 生成签名参数
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("X-Amz-Algorithm", "AWS4-HMAC-SHA256");
            queryParams.put("X-Amz-Date", formatTimestamp(Instant.now()));
            queryParams.put("X-Amz-SignedHeaders", "host");
            queryParams.put("X-Amz-Expires", String.valueOf(presignedUrlDuration));
            queryParams.put("X-Amz-Credential", generateCredential());
            // 注意：这是一个简化实现，实际生产环境建议使用官方presigner
            String baseUrl = String.format("https://%s%s", host, canonicalUri);
            StringBuilder queryString = new StringBuilder();
            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                if (!queryString.isEmpty()) {
                    queryString.append("&");
                }
                queryString.append(entry.getKey()).append("=").append(entry.getValue());
            }
            return baseUrl + "?" + queryString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成预签名GET URL失败", e);
        }
    }

    /**
     * 生成AWS凭证字符串
     */
    private String generateCredential() {
        String date = DateTimeFormatter.ofPattern("yyyyMMdd")
                .withZone(ZoneOffset.UTC)
                .format(Instant.now());
        // 在EC2环境中，可以通过实例角色获取
        return String.format("${aws.access.key.id}/%s/%s/s3/aws4_request", date, region);
    }

    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Instant instant) {
        return DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'")
                .withZone(ZoneOffset.UTC)
                .format(instant);
    }

    /**
     * URL编码Key
     */
    private String encodeKey(String key) {
        try {
            return URLEncoder.encode(key, StandardCharsets.UTF_8)
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            return key;
        }
    }

    /**
     * 生成文件key
     */
    private String generateFileKey(String fileName, String folder) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileExtension = getFileExtension(fileName);
        String newFileName = uuid + (fileExtension.isEmpty() ? "" : "." + fileExtension);
        if (folder != null && !folder.trim().isEmpty()) {
            folder = folder.trim().replaceAll("^/+", "").replaceAll("/+$", "");
            return folder + "/" + newFileName;
        }
        return newFileName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 生成下载URL
     */
    private String generateDownloadUrl(String fileKey) {
        if (cdnDomain != null && !cdnDomain.trim().isEmpty()) {
            // 使用CDN域名
            return cdnDomain.trim().replaceAll("/+$", "") + "/" + fileKey;
        } else {
            // 使用S3默认域名
            return String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, fileKey);
        }
    }

    /**
     * 生成上传表单字段
     */
    private Map<String, String> generateUploadFields(String key, String contentType) {
        Map<String, String> fields = new HashMap<>();
        fields.put("key", key);
        fields.put("Content-Type", contentType);
        return fields;
    }

    /**
     * 根据文件名获取内容类型
     */
    private String getContentTypeFromFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "application/octet-stream";
        }
        String extension = getFileExtension(fileName).toLowerCase();
        return switch (extension) {
            case "jpg", "jpeg" -> "image/jpeg";
            case "png" -> "image/png";
            case "gif" -> "image/gif";
            case "pdf" -> "application/pdf";
            case "txt" -> "text/plain";
            case "doc" -> "application/msword";
            case "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls" -> "application/vnd.ms-excel";
            case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "zip" -> "application/zip";
            case "mp4" -> "video/mp4";
            case "mp3" -> "audio/mpeg";
            default -> "application/octet-stream";
        };
    }

    /**
     * 预签名上传信息
     */
    @Data
    @Builder
    public static class PresignedUploadInfo {
        private String uploadUrl;
        private String downloadUrl;
        private String fileKey;
        private String fileName;
        private Long expiresIn;
        private Map<String, String> fields;
    }

    /**
     * 文件信息
     */
    @Data
    @Builder
    public static class FileInfo {
        private String fileKey;
        private String contentType;
        private Long contentLength;
        private java.time.Instant lastModified;
        private String etag;
        private String downloadUrl;
    }
}
