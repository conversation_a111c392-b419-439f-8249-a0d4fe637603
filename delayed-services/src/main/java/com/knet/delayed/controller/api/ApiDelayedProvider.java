package com.knet.delayed.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.delayed.service.IRedisDelayedQueueService;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:52
 * @description: 商品服务-对外提供接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "延迟服务-对外提供接口", description = "延迟服务-对外提供接口")
public class ApiDelayedProvider {
    @Resource
    private IRedisDelayedQueueService redisDelayedQueueService;

    /**
     * 添加延迟消息
     *
     * @param delayedMessage 延迟消息
     * @return 添加结果
     * @apiNote 此方法，需要在调用方，自主配置完成
     */
    @Schema(description = "添加延迟消息")
    @PostMapping("/add")
    public HttpResult<String> addDelayedMessage(@RequestBody DelayedMessage delayedMessage) {
        log.info("添加延迟消息: {}", delayedMessage);
        redisDelayedQueueService.addDelayMessage(delayedMessage);
        return HttpResult.ok("Delayed message added!");
    }

    /**
     * 取消延迟消息
     *
     * @param orderId 订单ID
     * @return 取消结果
     */
    @Schema(description = "取消延迟消息")
    @PostMapping("/cancel/{orderId}")
    public HttpResult<String> cancelDelayedMessage(@PathVariable("orderId") String orderId) {
        log.info("取消延迟消息: orderId={}", orderId);
        boolean cancelled = redisDelayedQueueService.cancelDelayedMessage(orderId);
        if (cancelled) {
            return HttpResult.ok("Delayed message cancelled!");
        } else {
            return HttpResult.ok("No delayed message found for orderId: " + orderId);
        }
    }

    /**
     * 根据消息ID取消延迟消息
     *
     * @param messageId 消息ID
     * @return 取消结果
     */
    @Schema(description = "根据消息ID取消延迟消息")
    @PostMapping("/cancel/message/{messageId}")
    public HttpResult<String> cancelDelayedMessageById(@PathVariable("messageId") String messageId) {
        log.info("取消延迟消息: messageId={}", messageId);
        boolean cancelled = redisDelayedQueueService.cancelDelayedMessageById(messageId);
        if (cancelled) {
            return HttpResult.ok("Delayed message cancelled!");
        } else {
            return HttpResult.ok("No delayed message found for messageId: " + messageId);
        }
    }
}
