package com.knet.delayed.service.impl;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.delayed.service.IRedisDelayedQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:47
 * @description:
 */
@Slf4j
@Service
public class RedisDelayedQueueServiceImpl implements IRedisDelayedQueueService {

    private static final String DELAYED_QUEUE_KEY = "delayed_queue:zset";

    /**
     * 添加延迟消息到队列
     *
     * @param message 延迟消息
     */
    @Override
    public void addDelayMessage(DelayedMessage message) {
        String messageJson = JSON.toJSONString(message);
        RedisCacheUtil.addZSet(DELAYED_QUEUE_KEY, messageJson, message.getTriggerTime());
        log.info("添加延迟消息到队列: messageId={}, orderId={}, triggerTime={}",
                message.getId(), extractOrderIdFromPayload(message.getPayloadJson()), message.getTriggerTime());
    }

    /**
     * 取消延迟消息
     * 根据订单ID取消对应的延迟消息，防止已取消订单的延迟消息被触发
     *
     * @param orderId 订单ID
     * @return 是否取消成功
     */
    @Override
    public boolean cancelDelayedMessage(String orderId) {
        try {
            Set<Object> allMessages = RedisCacheUtil.getZSetAll(DELAYED_QUEUE_KEY);
            if (allMessages == null || allMessages.isEmpty()) {
                log.info("延迟队列为空，无需取消: orderId={}", orderId);
                return false;
            }
            int cancelledCount = 0;
            for (Object obj : allMessages) {
                String jsonStr = (String) obj;
                DelayedMessage msg = JSON.parseObject(jsonStr, DelayedMessage.class);
                // 从消息体中提取订单ID进行匹配
                String messageOrderId = extractOrderIdFromPayload(msg.getPayloadJson());
                if (orderId.equals(messageOrderId)) {
                    // 从ZSet中移除该消息
                    RedisCacheUtil.removeZSet(DELAYED_QUEUE_KEY, obj);
                    cancelledCount++;
                    log.info("成功取消延迟消息: messageId={}, orderId={}", msg.getId(), orderId);
                }
            }
            if (cancelledCount > 0) {
                log.info("成功取消 {} 条延迟消息: orderId={}", cancelledCount, orderId);
                return true;
            } else {
                log.info("未找到需要取消的延迟消息: orderId={}", orderId);
                return false;
            }
        } catch (Exception e) {
            log.error("取消延迟消息失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据消息ID取消延迟消息
     *
     * @param messageId 消息ID
     * @return 是否取消成功
     */
    @Override
    public boolean cancelDelayedMessageById(String messageId) {
        try {
            // 获取所有延迟消息
            Set<Object> allMessages = RedisCacheUtil.getZSetAll(DELAYED_QUEUE_KEY);
            if (allMessages == null || allMessages.isEmpty()) {
                log.info("延迟队列为空，无需取消: messageId={}", messageId);
                return false;
            }

            for (Object obj : allMessages) {
                String jsonStr = (String) obj;
                DelayedMessage msg = JSON.parseObject(jsonStr, DelayedMessage.class);

                if (messageId.equals(msg.getId())) {
                    // 从ZSet中移除该消息
                    RedisCacheUtil.removeZSet(DELAYED_QUEUE_KEY, obj);
                    log.info("成功取消延迟消息: messageId={}", messageId);
                    return true;
                }
            }

            log.info("未找到需要取消的延迟消息: messageId={}", messageId);
            return false;
        } catch (Exception e) {
            log.error("取消延迟消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从消息体中提取订单ID
     *
     * @param payloadJson 消息体JSON
     * @return 订单ID
     */
    private String extractOrderIdFromPayload(String payloadJson) {
        try {
            return JSON.parseObject(payloadJson).getString("orderId");
        } catch (Exception e) {
            log.warn("提取订单ID失败: payloadJson={}, error={}", payloadJson, e.getMessage());
            return null;
        }
    }
}
